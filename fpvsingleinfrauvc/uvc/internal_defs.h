#pragma once
#ifndef __INTERNAL_DEFS_H__
#define __INTERNAL_DEFS_H__

#include <sys/time.h>
#include <time.h>

#define VII_INFRA_DEV   (2)
#define VII_VIS_DEV   (0)
#define VII_SRC_PIPE    (0)
#define VII_DST_PIPE    (3)
#define VII_VPP_GRP_ID 1
#define IR_VII_CHN_ID 1

#define IR_VPP_GRP_ID 3

#define IR_VPP_CHN_ID 3
#define IR_UVC_VPP_FHD_CHN_ID 0
#define IR_VPP_EXT_BIND_CHN_ID 0
#define IR_VPP_FHD_EXT_CHN_ID 0
#define IR_VPP_PIP_EXT_CHN_ID 1
#define IR_VPP_NN_NANOTRACK_EXT_CHN_ID 2
#define IR_VPP_NN_CHN_ID 1
#ifdef VII_FROM_UVC_640_SR_COMP
#define IR_VPP_UVC_RAW_ID 2
#define IR_VO_UVC_RAW_ID 1
#endif



#if INFERENCE_SAVED_JPEG_WITH_YOLO_MODE // INFERENCE_SAVED_JPEG_WITH_YOLO_MODE = 1 indicate draw detected box 
#define SEND_VENC_CHN_ID 1
#else
#define SEND_VENC_CHN_ID 0
#endif

#define IR_VPP_EXTCHN_ID 5
#define IR_VPP_SR_CHN_ID 3
#define IR_VII_ROUTE_CFG_ID 0
#define IR_VO_DEV_ID 0
#define IR_VO_CHN_ID 0

#define VIS_VII_SRC_PIPE (2)
#define VIS_VII_ROUTE_CFG_ID 1
#define VIS_VII_CHN_ID 2
#ifdef CVBS_VO_ENABLED
#ifdef QUICK_SWITCH_VO
//#define VIS_VPP_CHN_ID 4 // if NN chn1 for yolo is not enabled, will use chn1
#define VIS_VPP_CHN_ID 1
#else
#define VIS_VPP_CHN_ID 3
#endif
#else
#define VIS_VPP_CHN_ID 0
#endif
#define VIS_VPP_GRP_ID 0
#define VIS_VPP_NN_CHN_ID 1
#define VIS_VPP_TRACK_NN_CHN_ID 2 
#define VIS_VPP_PIP_CHN_ID 3

#define IR_VPP_GRP_SR = 3

#define DSP_ID          (0)

// #define WIDTH 640
// #define HEIGHT UVC_HEIGHT
#define SHUTTER_CYCLE_PER_N_FRAMES (600)

#define AIRVISEN_SAFE_FREE(ptr) \
    if (ptr != NULL) \
    { \
        free(ptr); \
        ptr = NULL; \
    }

#define AIRVISEN_TRACKER_RGN_CNT (8)
#define AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE (43)
#define AIRVISEN_TRACKER_DRAW_TRIANGLE_HOR_RGN_HANDLE (44)
#define AIRVISEN_TRACKER_DRAW_TRIANGLE_VER_RGN_HANDLE (45)

#define AIRVISEN_TRACKER_DRAW_TEXT_LEFT_RGN_HANDLE (46)

#define AIRVISEN_TRACKER_DRAW_4K_LOGO_RGN_HANDLE (60)


extern int g_traceinfo;

static int get_traceinfo()
{
    return g_traceinfo;
}

static int set_traceinfo(int enable)
{
    g_traceinfo = enable;
    return g_traceinfo;
}


#define LOG_TAG "airvisen"
#define airvisen_trace(fmt, ...) \
    if (g_traceinfo == 1) \
    { \
        printf("%s: %s->%s[%d]: " fmt, LOG_TAG, __FILE__, __func__, __LINE__, ##__VA_ARGS__); \
    }

#define PRINTLINE airvisen_trace("func:%s, line:%d\n", __FUNCTION__, __LINE__);


#if defined(ENABLE_TIME_COST_PRINT)

#define WALLTIMETRACKING  {\
    struct  timeval    start_run; \
    struct  timeval   end_run;   \
    gettimeofday(&start_run,NULL);

#define WALLTIMESTAT(msg)  gettimeofday(&end_run,NULL); \
    long diff = 1000000 * (end_run.tv_sec-start_run.tv_sec)+ end_run.tv_usec-start_run.tv_usec; \
         airvisen_trace("%s, WallTime diff is %ld s, %ld us\n", msg, diff/1000000, diff%1000000); \
}

#define WALLTIMETRACKING_COUNTER {\
    static struct  timeval    start_run; \
    static struct  timeval   end_run;   \
    static int counter = 0; \
    static long diff = 0; \
    gettimeofday(&start_run,NULL); \
    counter++; 

#define WALLTIMESTAT_COUNTER(msg, n) \
    gettimeofday(&end_run,NULL); \
    diff += 1000000 * (end_run.tv_sec-start_run.tv_sec)+ end_run.tv_usec-start_run.tv_usec; \
    if(counter==n) { \
        long avg = diff/counter; \
        airvisen_trace("%s, WallTime diff is %ld s, %ld us\n", msg, avg/1000000, avg%1000000); \
        counter = 0; \
        diff = 0; \
    } \
}

#define STARTCLOCK { \
	clock_t start_t, finish_t; \
	double total_t = 0; \
    start_t = clock(); 


#define ENDCLOCK(msg) finish_t = clock(); \
				total_t = (double)(finish_t - start_t) / CLOCKS_PER_SEC * 1000;\
		airvisen_trace("%s, timecost: %.3f (ms)\n", msg, total_t); \
}

#else
#define WALLTIMETRACKING  
#define WALLTIMESTAT(msg)
#define WALLTIMETRACKING_COUNTER
#define WALLTIMESTAT_COUNTER(msg, n)
#define STARTCLOCK
#define ENDCLOCK(msg)
#endif

#define FPS_STATISTIC_START  \
    clock_t fps_start_t, fps_finish_t; \
    double fps_total_t = 0; \
    fps_start_t = clock(); 

#define FPS_STATISTIC_UPDATE(nLoop) \
    fps_finish_t = clock(); \
    fps_total_t = (double)(fps_finish_t - fps_start_t) / CLOCKS_PER_SEC * 1000;\
    printf("%d, %.3f(ms), FPS: %.1f\n", nLoop, fps_total_t, 1000/(fps_total_t/nLoop)); 

// using namespace std::chrono;
#define FPS_MEASURE(proc_name, time_interval) \
{ \
    static auto lastTime = std::chrono::high_resolution_clock::now(); \
    static int count = 0; \
    count++; \
    auto currentTime = std::chrono::high_resolution_clock::now(); \
    std::chrono::duration<double> elapsed = currentTime - lastTime; \
    if (elapsed.count() >= time_interval) { \
        double fps = count / elapsed.count(); \
        airvisen_trace("[%s] FPS over last %d seconds: %lf\n", proc_name, time_interval, fps); \
        count = 0; \
        lastTime = currentTime; \
    } \
} 


#include <unistd.h>
#include <stdio.h>
static void waitForPause(void)
{
    printf("\n=====Press enter to exit=====\n");
    while (1) {
        if (getchar() == '\n') {
            break;
        }
        printf("please press Enter to exit....\n");
        //usleep(1000);
    };
    printf("\n=====exit=====\n");
}

 
#endif
