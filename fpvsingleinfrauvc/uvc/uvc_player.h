/**
 * @file    uvc_player.h
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#ifndef _VS_UVC_PLAYER_H__
#define _VS_UVC_PLAYER_H__
#include "vs_type.h"
#include "vs_mal_vbm.h"
#include "sample_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct vs_uvc_player_info {
    //player msg
    vs_int32_t                  vdec_chnnum;
    vs_int32_t                  vdec_chnid;
    vs_payload_type_e           decode_type;
    vs_size_s                   disp_size;
    sample_vdec_cfg_s           vdec_config;

    vs_int32_t                  vpp_grpid;
    vs_int32_t                  vpp_chnid;
    vs_int32_t                  vpp_fhd_chnid;
    vs_bool_t                   vpp_chn_enable[VPP_MAX_PHYCHN_NUM];

    vs_uint32_t                 vo_chnid;
    sample_vo_cfg_s             vo_config;
} vs_uvc_player_info_s;

vs_int32_t uvc_player_create(vs_void_t* p_camera_info);
vs_int32_t uvc_player_destroy(vs_void_t* p_camera_info);
vs_int32_t uvc_player_framesend(vs_void_t* p_camera_info);

#ifdef __cplusplus
}
#endif

#endif /*_VS_UVC_PLAYER_H__*/

