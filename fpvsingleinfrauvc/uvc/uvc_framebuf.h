/**
 * @file    uvc_framebuf.h
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#ifndef _VS_UVC_FRAMEBUF_H__
#define _VS_UVC_FRAMEBUF_H__
#include <linux/videodev2.h>
#include "vs_type.h"
#include "vs_comm_video.h"
#include "vs_mal_vbm.h"
#ifdef __cplusplus
extern "C" {
#endif

typedef enum vs_uvc_framebuf_status {
    E_UVC_FRAMEBUF_STATUS_RELEASE = 0,
    E_UVC_FRAMEBUF_STATUS_ACQUIRE,
    E_UVC_FRAMEBUF_STATUS_MAX
} vs_uvc_framebuf_status_e;

typedef struct vs_uvc_framebuffer {
    struct v4l2_buffer          buf;
    vs_void_t                   *start;
    vs_uint32_t                 length;

    vs_video_frame_info_s       frame_info;

    vs_uvc_framebuf_status_e    status;
} vs_uvc_framebuffer_s;

vs_int32_t uvc_framebuf_init(vs_void_t* p_camera_info);
vs_int32_t uvc_framebuf_deinit(vs_void_t* p_camera_info);
vs_int32_t uvc_framebuf_dqbuf(vs_void_t* p_camera_info);
vs_int32_t uvc_framebuf_qbuf(vs_void_t* p_camera_info);

vs_int32_t uvc_framebuf_vb_acquire(vs_void_t* p_camera_info);
vs_int32_t uvc_framebuf_vb_release(vs_void_t *p_camera_info);


#ifdef __cplusplus
}
#endif

#endif /*_VS_UVC_FRAMEBUF_H__*/
