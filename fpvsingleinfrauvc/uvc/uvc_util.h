/**
 * @file    uvc_util.h
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#ifndef _VS_UVC_UTIL_H__
#define _VS_UVC_UTIL_H__
#include "vs_type.h"
#include "vs_comm.h"
#include "vs_mal_log.h"


#ifdef __cplusplus
extern "C" {
#endif

#define UVC_LOG_PRINTF

#define V4L2_PIX_FMT_HEVC     v4l2_fourcc('H', 'E', 'V', 'C') /* HEVC aka H.265 */
#define V4L2_PIX_FMT_H265     v4l2_fourcc('H', '2', '6', '5') /* add for uvc */
#define V4L2_PIX_FMT_MJPEG    v4l2_fourcc('M', 'J', 'P', 'G') /* Motion-JPEG   */
#define V4L2_PIX_FMT_JPEG     v4l2_fourcc('J', 'P', 'E', 'G') /* JFIF JPEG     */
#define V4L2_PIX_FMT_H264     v4l2_fourcc('H', '2', '6', '4') /* H264 with start codes */

#define REMOVE_FILE_PATH(x)     (strrchr(x,'/')?strrchr(x,'/')+1:x)

#ifdef UVC_LOG_PRINTF
#define VS_UVC_LOG_E(fmt, args...)  \
            do{ \
                vs_log(E_MOD_ID_USER, VS_LOG_ERROR, (const char *)"\n\033[0;31m [%s:%d]<%s>:" fmt "\033[0;39m", REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
            } while (0)

#define VS_UVC_LOG_W(fmt, args...)  \
            do{ \
                vs_log(E_MOD_ID_USER, VS_LOG_WARN, (const char *)"\n\033[0;31m [%s:%d]<%s>:" fmt "\033[0;39m", REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
            } while (0)

#define VS_UVC_LOG_I(fmt, args...)  \
            do{ \
                vs_log(E_MOD_ID_USER, VS_LOG_INFO, (const char *)"[%s:%d]<%s>: " fmt, REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
            } while (0)

#define VS_UVC_LOG_D(fmt, args...)  \
            do{ \
                vs_log(E_MOD_ID_USER, VS_LOG_DEBUG, (const char *)"[%s:%d]<%s>: " fmt, REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
            } while (0)
#else
#define VS_UVC_LOG_E(fmt, args...)  \
        do{ \
            printf("\n\033[0;31m [%s:%d]<%s>:" fmt "\033[0;39m", REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
        } while (0)

#define VS_UVC_LOG_W(fmt, args...)  \
        do{ \
            printf("\n\033[0;31m [%s:%d]<%s>:" fmt "\033[0;39m", REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
        } while (0)

#define VS_UVC_LOG_I(fmt, args...)  \
        do{ \
            printf("[%s:%d]<%s>: " fmt, REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
        } while (0)

#define VS_UVC_LOG_D(fmt, args...)  \
        do{ \
            printf("[%s:%d]<%s>: " fmt, REMOVE_FILE_PATH(__FILE__), __LINE__, __FUNCTION__, ##args);\
        } while (0)
#endif

vs_uint64_t vs_uvc_util_time_get(vs_void_t);
vs_char_t *vs_uvc_util_time_get_str(vs_void_t);
vs_char_t *vs_uvc_v4l2_pixelformat_str_get(vs_uint32_t pixelformat);


#ifdef __cplusplus
}
#endif

#endif /*_VS_UVC_UTIL_H__*/
