/**
 * @file    uvc_player.c
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <linux/videodev2.h>
#include <vs_type.h>

#include "uvc_player.h"
#include "uvc_camera.h"
#include "uvc_util.h"
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"

#ifdef FRAME_CTRL_ENABLED
#include <stdio.h>
#include <time.h>

#define INPUT_FRAME_RATE 50  // 输入帧率50Hz
#define OUTPUT_FRAME_RATE 30 // 输出帧率30Hz
#define FRAME_INTERVAL_NS   (1000000000ULL / OUTPUT_FRAME_RATE) // 输出帧间隔（纳秒）

static struct timespec last_output_time = {0, 0}; // 上次输出时间戳
static int frame_count = 0; // 输入帧计数器

int av_frc() {
        struct timespec current_time;
        clock_gettime(CLOCK_MONOTONIC, &current_time); // 获取当前时间
        // 计算时间差（纳秒）
        int64_t elapsed_ns = (current_time.tv_sec - last_output_time.tv_sec) * 1000000000ULL +
                             (current_time.tv_nsec - last_output_time.tv_nsec);

        // 判断是否达到输出间隔
        if (elapsed_ns >= FRAME_INTERVAL_NS) {
            // 输出帧（此处用打印模拟）
            printf("Output frame %d at %ld ns\n", frame_count + 1, current_time.tv_nsec);

            // 更新时间戳和计数器
            last_output_time = current_time;
            frame_count++;
            return 1;
        }

    return 0;
}
#endif //< FRAME_CTRL_ENABLED

#ifdef VENC_ENABLED
static vs_payload_type_e g_encode_type = E_PT_TYPE_H264;
#define SAMPLE_VENC_INPUT_FORMAT       E_PIXEL_FORMAT_YUV_420SP
static vs_compress_mode_e  g_compress_mode = E_COMPRESS_MODE_NONE;
#endif

extern vs_int32_t infra_player_procedure(vs_void_t* ptrSrc, vs_int32_t srcStride, vs_void_t* ptrDst, vs_uint32_t dstStride);

static vs_vo_output_type_e g_interface_output = E_VO_OUTPUT_TYPE_1080P60;

static vs_int32_t uvc_player_sys_init(vs_size_s input_size)
{
    vs_vb_cfg_s vb_cfg;
    vs_int32_t ret = VS_SUCCESS;

    memset(&vb_cfg, 0, sizeof(vs_vb_cfg_s));
    vb_cfg.pool_cnt = 1;
    vb_cfg.ast_commpool[0].blk_size = STRIDE_UP(input_size.width * input_size.height * 2, 4096);
    //vb_cfg.ast_commpool[0].blk_cnt = 10;
    vb_cfg.ast_commpool[0].blk_cnt = 40;
    vb_cfg.ast_commpool[0].remap_mode = VB_REMAP_MODE_NONE;
    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_sys_init failed, ret[0x%x] blk_size[%llu] blk_cnt[%u] remap_mode[%d]\n", ret,
                        vb_cfg.ast_commpool[0].blk_size, vb_cfg.ast_commpool[0].blk_cnt, vb_cfg.ast_commpool[0].remap_mode);
        return ret;
    }
    return VS_SUCCESS;
}

#ifdef VENC_ENABLED
vs_int32_t sample_venc_chn_init(vs_int32_t venc_chnid, vs_payload_type_e type, vs_venc_profile_e profile,
               vs_size_s frame_size, sample_brc_mode_e brc_mode, vs_venc_gop_attr_s *p_gop_attr)

{
    vs_int32_t ret = VS_SUCCESS;
    sample_venc_cfg_s sample_venc_cfg;

    memset(&sample_venc_cfg, 0, sizeof(sample_venc_cfg_s));
    sample_venc_cfg.format = SAMPLE_VENC_INPUT_FORMAT;
    sample_venc_cfg.compress = (g_compress_mode == E_COMPRESS_MODE_NONE) ? VS_FALSE : VS_TRUE;
    sample_venc_cfg.type = type;
    sample_venc_cfg.profile = profile;
    sample_venc_cfg.frame_size = frame_size;
    sample_venc_cfg.brc_mode = brc_mode;
    sample_venc_cfg.frc.src_framerate = 50;
    sample_venc_cfg.frc.dst_framerate = 30;
        sample_venc_cfg.bandwidth_save_strength = 0;
    if (p_gop_attr != NULL) {
        sample_venc_cfg.gop_attr = *p_gop_attr;
    }
    ret = sample_common_venc_start(venc_chnid, &sample_venc_cfg);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_common_venc_start failed, ret[0x%x]\n", ret);
        return ret;
    }

    return ret;
}

#endif
static vs_int32_t uvc_player_vpp_init(vs_int32_t vpp_grpid, vs_size_s input_size, vs_size_s *p_output_size, vs_bool_t *p_chn_enable)
{
    vs_int32_t i = 0, ret = 0;
    vs_vpp_grp_attr_s vpp_grp_attr;
    vs_vpp_chn_attr_s vpp_chn_attr[VPP_MAX_PHYCHN_NUM];

    memset(&vpp_grp_attr, 0, sizeof(vpp_grp_attr));
#ifdef FHD_OUTPUT_ENABLED
    vpp_grp_attr.max_width = 1920;
    vpp_grp_attr.max_height = 1080;
#else
    vpp_grp_attr.max_width = input_size.width;
    vpp_grp_attr.max_height = input_size.height;
#endif
    vpp_grp_attr.dynamic_range = E_DYNAMIC_RANGE_SDR8;
    vpp_grp_attr.pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    vpp_grp_attr.framerate.dst_framerate = -1;
    vpp_grp_attr.framerate.src_framerate = -1;

    memset(&vpp_chn_attr, 0, sizeof(vpp_chn_attr));
    for (i = 0; i < VPP_MAX_PHYCHN_NUM; i++) {
        if (p_chn_enable[i] == VS_TRUE) {
            vpp_chn_attr[i].chn_mode = E_VPP_CHN_MODE_USER;
            vpp_chn_attr[i].width = p_output_size[i].width;
            vpp_chn_attr[i].height = p_output_size[i].height;
            printf("vpp_chn:%d, width:%d, height:%d\n", i, vpp_chn_attr[i].width, vpp_chn_attr[i].height);
            vpp_chn_attr[i].video_format = E_VIDEO_FORMAT_LINEAR;
            vpp_chn_attr[i].pixel_format = E_PIXEL_FORMAT_YUV_420SP;
            vpp_chn_attr[i].dynamic_range = E_DYNAMIC_RANGE_SDR8;
            vpp_chn_attr[i].compress_mode = E_COMPRESS_MODE_NONE;
            vpp_chn_attr[i].framerate.src_framerate = -1;
            vpp_chn_attr[i].framerate.dst_framerate = -1;
            vpp_chn_attr[i].mirror_enable = VS_FALSE;
            vpp_chn_attr[i].flip_enable = VS_FALSE;
            vpp_chn_attr[i].depth = 3;
            if (i == IR_UVC_VPP_FHD_CHN_ID)
            {
                vpp_chn_attr[i].depth = 3;
            }
            if (i == IR_VPP_CHN_ID)
            {
                vpp_chn_attr[i].depth = 3;
            }

#ifdef VPP_EXT_FHD_OUTPUT_ENABLED
            if (i == IR_VPP_EXT_BIND_CHN_ID)
            {
                vpp_chn_attr[i].depth = 0;
            }
#endif

            vpp_chn_attr[i].aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
        }
    }
#ifdef VPP_EXT_FHD_OUTPUT_ENABLED
    vs_vpp_extchn_attr_s vpp_extchn_attr[VPP_MAX_EXTCHN_NUM];
    vs_vpp_extchn_attr_s *p_extchn_attr = &vpp_extchn_attr[IR_VPP_FHD_EXT_CHN_ID];
    p_extchn_attr->bind_chnid = IR_VPP_EXT_BIND_CHN_ID;
    p_extchn_attr->width = 1920;
    p_extchn_attr->height = 1080;
    p_extchn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    p_extchn_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    p_extchn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    p_extchn_attr->compress_mode = E_COMPRESS_MODE_NONE;
    p_extchn_attr->framerate.src_framerate = -1;
    p_extchn_attr->framerate.dst_framerate = -1;
    p_extchn_attr->depth = 3;
 

//    p_extchn_attr = &vpp_extchn_attr[IR_VPP_PIP_EXT_CHN_ID];
//    p_extchn_attr->bind_chnid = IR_VPP_EXT_BIND_CHN_ID;
//    p_extchn_attr->width = UVC_WIDTH;
//    p_extchn_attr->height = UVC_HEIGHT;
//    p_extchn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
//    p_extchn_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
//    p_extchn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
//    p_extchn_attr->compress_mode = E_COMPRESS_MODE_NONE;
//    p_extchn_attr->framerate.src_framerate = -1;
//    p_extchn_attr->framerate.dst_framerate = -1;
//    p_extchn_attr->depth = 6;
// #ifdef NN_NANOTRACK_ENABLED
//     p_extchn_attr = &vpp_extchn_attr[IR_VPP_NN_NANOTRACK_EXT_CHN_ID];
//     p_extchn_attr->bind_chnid = IR_VPP_EXT_BIND_CHN_ID;
//     p_extchn_attr->width = 1920;
//     p_extchn_attr->height = 1080;
//     p_extchn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
//     p_extchn_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
//     p_extchn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
//     p_extchn_attr->compress_mode = E_COMPRESS_MODE_NONE;
//     p_extchn_attr->framerate.src_framerate = -1;
//     p_extchn_attr->framerate.dst_framerate = -1;
//     p_extchn_attr->depth = 6;
// #endif
#endif

    ret = sample_common_vpp_start(vpp_grpid, p_chn_enable, &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vpp_start failed, ret[0x%x]\n", ret);
        return ret;
    }
#ifdef VPP_EXT_FHD_OUTPUT_ENABLED
    vs_bool_t vpp_extchn_enable[VPP_MAX_EXTCHN_NUM] = {VS_FALSE};
    vpp_extchn_enable[IR_VPP_FHD_EXT_CHN_ID] = VS_TRUE;
    //vpp_extchn_enable[IR_VPP_PIP_EXT_CHN_ID] = VS_TRUE;
    //vpp_extchn_enable[IR_VPP_NN_NANOTRACK_EXT_CHN_ID] = VS_TRUE;
    ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
            vpp_extchn_enable, vpp_extchn_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("failed to set vpp extchn attr\n");
        return ret;
    }
#endif
    return ret;
}

static vs_int32_t uvc_palyer_vo_init(sample_vo_cfg_s *p_vo_config, vs_size_s input_size, vs_int32_t frame_rate)
{
    vs_int32_t ret = VS_SUCCESS;

    p_vo_config->vo_devid = 0;
    p_vo_config->vo_layerid = 0;
    p_vo_config->vo_output = g_interface_output;
    p_vo_config->bg_color = 0;
    p_vo_config->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    p_vo_config->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    p_vo_config->vo_mode = E_VO_MODE_1MUX;
    p_vo_config->img_width = input_size.width;
    p_vo_config->img_height = input_size.height;
    p_vo_config->zorder = 0;
#ifndef VS_ORION
    p_vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_HDMI;
    if ((input_size.width > PIC_1080P_WIDTH) || (input_size.height > PIC_1080P_HEIGHT)) {
        p_vo_config->vo_output = E_VO_OUTPUT_TYPE_3840x2160_60;
    } else if ((input_size.width <= PIC_1080P_WIDTH) && (input_size.height <= PIC_1080P_HEIGHT)) {
        p_vo_config->vo_output = E_VO_OUTPUT_TYPE_1080P60;
    } else {
        VS_UVC_LOG_E("width[%d] height[%d] vo_output not support\n", input_size.width, input_size.height);
        return VS_FAILED;
    }
#else
    p_vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_BT1120;
    if ((input_size.width > PIC_1080P_WIDTH) || (input_size.height > PIC_1080P_HEIGHT)) {
        p_vo_config->vo_output = E_VO_OUTPUT_TYPE_3840x2160_60;
    } else if ((input_size.width <= PIC_1080P_WIDTH) && (input_size.height <= PIC_1080P_HEIGHT)) {
        p_vo_config->vo_output = E_VO_OUTPUT_TYPE_1080P60;
    } else {
        VS_UVC_LOG_E("width[%d] height[%d] vo_output not support\n", input_size.width, input_size.height);
        return VS_FAILED;
    }
#endif

#if 0
    ret = sample_common_vo_start(p_vo_config);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vo_start failed, ret[0x%x]\n", ret);
    }
#endif
    return ret;
}

vs_int32_t uvc_player_create(vs_void_t* p_camera_info)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_uvc_player_info_s *p_player;
    /*vs_size_s vdec_size;
    vs_int32_t vdec_chnnum = 1;
    vs_int32_t vdec_chnid = 0;
    vs_payload_type_e decode_type = 0;
    sample_vo_cfg_s vo_config;
    sample_vdec_cfg_s vdec_config;*/
    vs_uvc_camera_info_s* p_camera = p_camera_info;
    vs_size_s vpp_output_size[VPP_MAX_PHYCHN_NUM] = {0};
    vs_size_s fhd_size = {1920, 1080};

    p_player = &p_camera->player;
    p_player->vdec_chnnum = 1;
    p_player->vdec_chnid = 0;
    p_player->vo_chnid = 0;
    p_player->vpp_grpid = IR_VPP_GRP_ID;
    p_player->vpp_chnid = IR_VPP_CHN_ID;
#ifdef VPP_EXT_FHD_OUTPUT_ENABLED
    p_player->vpp_fhd_chnid = IR_VPP_EXT_BIND_CHN_ID;
#else
    p_player->vpp_fhd_chnid = IR_UVC_VPP_FHD_CHN_ID; //< only chn0 can zoom in
#endif
    p_player->vpp_chn_enable[0] = VS_FALSE;
    p_player->vpp_chn_enable[1] = VS_FALSE;
    p_player->vpp_chn_enable[2] = VS_FALSE;
    p_player->vpp_chn_enable[IR_VPP_CHN_ID] = VS_TRUE;
    //p_player->vpp_chn_enable[IR_UVC_VPP_FHD_CHN_ID] = VS_TRUE;
#ifdef FHD_OUTPUT_ENABLED
    p_player->vpp_chn_enable[IR_VPP_EXT_BIND_CHN_ID] = VS_TRUE;
#endif

#ifdef VPP_FHD_OUTPUT_ENABLED
    p_player->vpp_chn_enable[p_player->vpp_fhd_chnid] = VS_TRUE;
#endif
#ifdef VPP_EXT_FHD_OUTPUT_ENABLED
    p_player->vpp_chn_enable[IR_VPP_EXT_BIND_CHN_ID] = VS_TRUE;
#endif
#ifdef NN_DETECT_ENABLED
   p_player->vpp_chn_enable[IR_UVC_VPP_NN_DETECT_CHN_ID] = VS_TRUE; 
#endif
#ifdef NN_NANOTRACK_ENABLED
   p_player->vpp_chn_enable[IR_UVC_VPP_NN_NANOTRACK_CHN_ID] = VS_TRUE; 
#endif

    p_player->disp_size.width = p_camera->width;
    p_player->disp_size.height = p_camera->height;
    printf("p_camera->width:%d, p_camera->height", p_camera->width, p_camera->height);

    vs_size_s vb_img_size;
    vb_img_size.width = p_camera->width;
    vb_img_size.height = p_camera->height;
#ifndef WORK_WITH_RGB
#ifdef FHD_OUTPUT_ENABLED
    vb_img_size.width = 1920;
    vb_img_size.height = 1080;
#endif
    ret = uvc_player_sys_init(vb_img_size);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_vdec_sys_init failed, ret[0x%x]\n", ret);
        goto exit_sys_exit;
    }
#endif

#ifdef VENC_ENABLED
    vs_int32_t venc_devid = 0;
    vs_int32_t venc_chnid[2] = {0, 1};

    vs_payload_type_e encode_type[1] = {g_encode_type};
    sample_brc_mode_e brc_mode = E_VENC_BRC_VBR;
    vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H264_MAIN};
    if (g_encode_type == E_PT_TYPE_H265)
    {
        profile[0] = E_VENC_PROFILE_H265_MAIN;
    }

    vs_venc_gop_attr_s gop_attr;
#ifdef GOP_MODE_NORMP
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_NORMP;
#else
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_LOWDELAYB;
#endif //< GOP_MODE_NORMP

#endif //< RTSP_ENABLED


    if (p_camera->pixelformat == V4L2_PIX_FMT_H264) {
        p_player->decode_type = E_PT_TYPE_H264;
    } else if (p_camera->pixelformat == V4L2_PIX_FMT_H265 || p_camera->pixelformat == V4L2_PIX_FMT_HEVC) {
        p_player->decode_type = E_PT_TYPE_H265;
    } else if (p_camera->pixelformat == V4L2_PIX_FMT_MJPEG) {
        p_player->decode_type = E_PT_TYPE_MJPEG;
    } else {
        p_player->decode_type = E_PT_TYPE_H264;
    }

#ifdef VO_ENABLED
    ret = uvc_palyer_vo_init(&p_player->vo_config, p_player->disp_size, p_camera->fps);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vo_start failed, ret[0x%x]\n", ret);
        goto exit_sys_exit;
    }
#endif
#ifdef VENC_ENABLED        
    ret = sample_common_venc_gop_attr_get(gop_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("gop_attr_get failed, ret[0x%x]\n", ret);
        return ret;
    }

#ifdef AIRVISEN_VENC_NALU_MODE_MULTI
    vs_venc_mod_param_s mod_param;
    if (g_encode_type == E_PT_TYPE_H265)
    {
        mod_param.type = E_VENC_MOD_TYPE_H265E;
    }
    else if (g_encode_type == E_PT_TYPE_H264)
    {
        mod_param.type = E_VENC_MOD_TYPE_H264E;
    }

    ret =  vs_mal_venc_mod_param_get(&mod_param);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_venc_mod_param_get failed, ret[0x%x]\n", ret);
        return ret;
    }
    mod_param.h265_param.union_nalu_mode = E_VENC_UNION_NALU_MODE_MULTI;
    mod_param.rcvstop_inputbuf_clear = VS_TRUE;
    ret =  vs_mal_venc_mod_param_set((const vs_venc_mod_param_s *)&mod_param);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_venc_mod_param_set failed, ret[0x%x]\n", ret);
        return ret;
    }
#endif
#ifdef VENC_OUTPUT_FHD 
    vs_size_s venc_infra_uvc_size = {1920, 1080};
#else
    vs_size_s venc_infra_uvc_size = {UVC_WIDTH, UVC_HEIGHT};
#endif
    ret = sample_venc_chn_init(IR_UVC_VENC_CHN_ID, encode_type[0], profile[0], venc_infra_uvc_size, brc_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_venc_chn_init failed, ret[0x%x]\n", ret     );
        return ret;
    }

#endif //< VENC_ENABLED

    //vpp_output_size[IR_VPP_CHN_ID] = p_player->disp_size;
#ifdef FHD_OUTPUT_ENABLED
    vpp_output_size[IR_VPP_EXT_BIND_CHN_ID] = p_player->disp_size;
#endif
    //vpp-grp3-chn3 
#ifdef VPP_EXT_FHD_OUTPUT_ENABLED 
    vpp_output_size[IR_VPP_EXT_BIND_CHN_ID] = p_player->disp_size;
#endif
    //vpp-grp3-chn3 
    vpp_output_size[IR_VPP_CHN_ID] = p_player->disp_size;
#ifdef VPP_FHD_OUTPUT_ENABLED
   vpp_output_size[IR_UVC_VPP_FHD_CHN_ID] = fhd_size;
#endif
#ifdef NN_DETECT_ENABLED
   vpp_output_size[IR_UVC_VPP_NN_DETECT_CHN_ID].width = NN_DETECT_WIDTH; 
   vpp_output_size[IR_UVC_VPP_NN_DETECT_CHN_ID].height = NN_DETECT_HEIGHT; 
#endif
#ifdef NN_NANOTRACK_ENABLED
   vpp_output_size[IR_UVC_VPP_NN_NANOTRACK_CHN_ID].width = NN_NANOTRACK_WIDTH; 
   vpp_output_size[IR_UVC_VPP_NN_NANOTRACK_CHN_ID].height = NN_NANOTRACK_HEIGHT; 
#endif
    ret = uvc_player_vpp_init(p_player->vpp_grpid, p_player->disp_size, vpp_output_size, p_player->vpp_chn_enable);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_player_vpp_init failed, ret[0x%x]\n", ret);
        goto exit_vo_stop;
    }
#ifdef WORK_WITH_2_MIPI
    ret = sample_common_vii_bind_vpp(3, 3, 3);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_common_vii_bind_vpp failed, ret[0x%x]\n     ", ret);
        goto exit_vpp_stop;
    }
#endif

#ifdef WORK_ALONE_WITHOUT_NN
#ifdef VO_ENABLED
    ret = sample_common_vpp_bind_vo(p_player->vpp_grpid, p_player->vpp_chnid, p_player->vo_config.vo_layerid, p_player->vo_chnid);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vpp_bind_vo failed, ret[0x%x]\n", ret);
        goto exit_vpp_stop;
    }

    ret = sample_common_vo_start(&p_player->vo_config);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vo_start failed, ret[0x%x]\n", ret);
    }
#endif //< VO_ENABLED
#ifdef VENC_ENABLED 
#ifdef VENC_OUTPUT_FHD 
        ret = sample_common_vpp_bind_venc(IR_UVC_VPP_GRP_ID, IR_UVC_VPP_FHD_CHN_ID, venc_devid, IR_UVC_VENC_CHN_ID);
#else
        ret = sample_common_vpp_bind_venc(IR_UVC_VPP_GRP_ID, IR_UVC_VPP_CHN_ID, venc_devid, IR_UVC_VENC_CHN_ID);
#endif
        if (ret != VS_SUCCESS) {
            vs_sample_trace("sample_common_vpp_bind_venc failed, ret[0x     %x]\n", ret);
            return ret;
        }
#endif
#endif

   
    return VS_SUCCESS;

#ifdef VENC_ENABLED
#ifdef WORK_ALONE_WITHOUT_NN
vs_sample_trace("exit_vpp_unbind_venc0\n");
#ifdef VENC_OUTPUT_FHD 
sample_common_vpp_unbind_venc(IR_UVC_VPP_GRP_ID, IR_UVC_VPP_FHD_CHN_ID, venc_devid, IR_UVC_VENC_CHN_ID);
#else
sample_common_vpp_unbind_venc(IR_UVC_VPP_GRP_ID, IR_UVC_VPP_CHN_ID, venc_devid, IR_UVC_VENC_CHN_ID);
#endif
#endif //< WORK_ALONE_WITHOUT_NN

vs_sample_trace("exit_venc0_stop\n");
sample_common_venc_stop(IR_UVC_VENC_CHN_ID);

#endif //< VENC_ENABLED

exit_vpp_unbind_vo:
#ifdef VO_ENABLED
    VS_UVC_LOG_I("exit_vpp_unbind_vo\n");
    sample_common_vpp_unbind_vo(p_player->vpp_grpid, p_player->vpp_chnid, p_player->vo_config.vo_layerid, p_player->vo_chnid);
#endif
exit_vpp_stop:
    VS_UVC_LOG_I("exit_vpp_stop\n");
    sample_common_vpp_stop(p_player->vpp_grpid, p_player->vpp_chn_enable);
exit_vo_stop:
#ifdef VO_ENABLED
    VS_UVC_LOG_I("exit_vo_stop.\n");
    sample_common_vo_stop(&p_player->vo_config);
#endif
exit_sys_exit:
    VS_UVC_LOG_I("exit_sys_exit.\n");
    sample_common_sys_exit();
    return ret;
}

vs_int32_t uvc_player_destroy(vs_void_t* p_camera_info)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_uvc_camera_info_s* p_camera = p_camera_info;
    vs_uvc_player_info_s *p_player = &p_camera->player;


    VS_UVC_LOG_I("exit_vpp_unbind_vo\n");
    ret = sample_common_vpp_unbind_vo(p_player->vpp_grpid, p_player->vpp_chnid, p_camera->player.vo_config.vo_layerid, p_camera->player.vo_chnid);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vdec_vb_pool_deinit failed, ret[0x%x]\n", ret);
    }

    VS_UVC_LOG_I("exit_vpp_stop\n");
    ret = sample_common_vpp_stop(p_player->vpp_grpid, p_player->vpp_chn_enable);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vdec_vb_pool_deinit failed, ret[0x%x]\n", ret);
    }

    VS_UVC_LOG_I("exit_vo_stop.\n");
    ret = sample_common_vo_stop(&p_camera->player.vo_config);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("sample_common_vo_stop failed, ret[0x%x]\n", ret);
    }

#ifndef WORK_WITH_RGB
    VS_UVC_LOG_I("exit_sys_exit.\n");
    sample_common_sys_exit();
#endif

    return ret;
}

static vs_void_t uvc_player_get_pixelformat_postfix(vs_uint32_t pixelformat, vs_char_t *p_postfix_buf, vs_int32_t buf_len)
{
    memset(p_postfix_buf, 0, buf_len);
    switch (pixelformat){
    case V4L2_PIX_FMT_H264:
        strcpy((char *)p_postfix_buf, "h264");
        break;
    case V4L2_PIX_FMT_H265:
    case V4L2_PIX_FMT_HEVC:
        strcpy((char *)p_postfix_buf, "h265");
        break;
    case V4L2_PIX_FMT_MJPEG:
        strcpy((char *)p_postfix_buf, "mjpeg");
        break;
    case V4L2_PIX_FMT_JPEG:
        strcpy((char *)p_postfix_buf, "jpeg");
        break;
    case V4L2_PIX_FMT_YUYV:
    case V4L2_PIX_FMT_NV12:
    case V4L2_PIX_FMT_NV21:
        strcpy((char *)p_postfix_buf, "yuv");
        break;
    default:
        VS_UVC_LOG_E("pixelformat[%u] not support!\n", pixelformat);
        strcpy((char *)p_postfix_buf, "UnknownEncodeType");
        break;
    }
}

static vs_int32_t uvc_player_write_file(vs_char_t *p_output_file_name, vs_char_t *p_data, vs_uint32_t datalen)
{
    FILE *p_out_file = NULL;

    p_out_file = fopen((char *)p_output_file_name, "a+");//wb");
    if (VS_NULL == p_out_file) {
	    VS_UVC_LOG_E("fopen output_path=%s error!\n", p_output_file_name);
		return VS_FAILED;
	}

    fwrite(p_data, 1, datalen, p_out_file);
    fflush(p_out_file);

    fclose(p_out_file);
    return VS_SUCCESS;
}

static vs_int32_t uvc_player_save_stream(vs_char_t *p_data, vs_uint32_t datalen, vs_uint32_t pixelformat)
{
    vs_char_t postfix_buf[128] = {0};
    vs_char_t output_file_name[256] = {0};

    uvc_player_get_pixelformat_postfix(pixelformat, postfix_buf, 128);
    sprintf((char *)output_file_name, "%s.%s",
                vs_uvc_v4l2_pixelformat_str_get(pixelformat), postfix_buf);
    uvc_player_write_file(output_file_name, p_data, datalen);
    VS_UVC_LOG_D("uvc_player_save_stream p_data[%p], datalen[%u] !\n", p_data, datalen);
    return VS_SUCCESS;
}

static vs_void_t uvc_player_yuyv_to_nv12(vs_char_t *p_yuyv, vs_char_t *p_nv12, vs_int32_t width, vs_int32_t height, vs_uint64_t yuyv_size)
{
    vs_int32_t pix_num = width * height;
    vs_uint32_t cycle_num = yuyv_size / pix_num / 2;
    vs_char_t *y = p_nv12;
    vs_char_t *uv = p_nv12 + pix_num ;
    vs_char_t *start = p_yuyv;
    vs_uint32_t i = 0, j = 0, k = 0, y_index = 0, uv_index = 0;

    for (i= 0; i < cycle_num; i++) {
        y_index = 0;
        for (j = 0; j < pix_num * 2; j = j + 2) {
           *(y + y_index) = *(start + j);
           y_index++;
        }
        start = p_yuyv + pix_num * 2 * i;
        y = y + pix_num * 3 / 2;
    }
#ifndef WORKING_ON_RAW_MODE
    start = p_yuyv;
    for (i= 0; i < cycle_num; i++) {
        uv_index = 0;
        for(j = 0; j < height; j = j + 2) {
           for(k = j * width * 2 + 1; k < width * 2 * (j + 1); k = k + 4) {
               *(uv + uv_index) = *(start + k);
               *(uv + uv_index + 1) = *(start + k + 2);
               uv_index += 2;
           }
        }
        start = p_yuyv + pix_num * 2 * i;
        uv = uv + pix_num * 3 / 2;
    }
#endif

}

static vs_void_t uvc_player_nv21_to_nv12(vs_char_t *p_nv21, vs_char_t *p_nv12, int width, int height)
{
    int i = 0, j = 0;
    int framesize = width * height;

    memcpy(p_nv21, p_nv12, framesize);

    for(i = 0; i < framesize; i++){
        p_nv12[i] = p_nv21[i];
    }

    for (j = 0; j < framesize / 2; j += 2) {
        p_nv12[framesize + j - 1] = p_nv21[j + framesize];
    }

    for (j = 0; j < framesize / 2; j += 2) {
        p_nv12[framesize + j] = p_nv21[j + framesize - 1];
    }
}

static int dumpToFile(char * ptrFileName, void *ptrData, unsigned int nDataLen)
{
    FILE *fp = NULL;

    fp = fopen(ptrFileName, "wb");
    if (fp != NULL)
    {
            size_t countW = fwrite(ptrData, 1, nDataLen, fp);
            fclose(fp);
            printf("dumpToFile:%s Success!\n", ptrFileName);
            return VS_SUCCESS;
    }

    printf("dumpToFile:%s Failed!\n", ptrFileName);
    return VS_FAILED;
}


static vs_int32_t sample_ive_mmz_malloc(vs_char_t *p_zone_name, vs_char_t *p_mmb_name, vs_uint32_t len,	vs_uint64_t *p_phys_addr, vs_void_t **pp_virt_addr)
{
	vs_int32_t ret = 0;

	ret = vs_mal_mmz_alloc(p_zone_name, p_mmb_name, len, p_phys_addr);
	if (0 != ret) {
		vs_sample_trace("phys_addr failed to alloc mmz! ret = 0x%x len = %d\n", ret, len);
		return VS_FAILED;
	}
	*pp_virt_addr = vs_mal_sys_mmap(*p_phys_addr, len);
	if (NULL == *pp_virt_addr) {
		vs_sample_trace("virt address failed to alloc mmz!\n");
		return VS_FAILED;
	}
	memset(*pp_virt_addr, 0, len);

    return VS_SUCCESS;
}

static vs_void_t sample_ive_mmz_free(vs_uint64_t phys_addr, vs_void_t *p_virt_addr, vs_uint32_t len)
{
	vs_int32_t ret;

    if (0 == phys_addr || NULL == p_virt_addr) {
        return;
    }
	ret = vs_mal_sys_unmap(p_virt_addr, len);
	if (0 != ret) {
        vs_sample_trace("sys_unmap ret = 0x%x\n", ret);
    }
	ret = vs_mal_mmz_free(phys_addr);
	if (0 != ret) {
        vs_sample_trace("free ext ret = 0x%x\n", ret);
    }
    return;

}



static vs_int32_t uvc_player_framesend_to_vpp(vs_uvc_camera_info_s* p_camera)
{
    vs_int32_t ret = VS_FAILED;
    vs_int32_t timeout_ms = 30;

    //airvisen_trace("uvc_player_framesend_to_vpp start pixelformat[%s].\n", vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat));
    ret = uvc_framebuf_vb_acquire(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_framebuf_vb_acquire failed: (0x%x).\n", ret);
       return VS_FAILED;;
    }
   
#ifdef FRAME_CTRL_ENABLED
    static int frameIdx = 0;
    frameIdx++;
    if (av_frc() != 1)
    {
        printf("framerate control will skip current frame %d!\n", frameIdx);
        goto exit_frc_skp;
    }
#endif
    if (p_camera->pixelformat == V4L2_PIX_FMT_YUYV) {
    
        uvc_player_yuyv_to_nv12((vs_char_t *)p_camera->buf[p_camera->buf_cur_index].start,
                                (vs_char_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0],
                                p_camera->buf[p_camera->buf_cur_index].frame_info.frame.width,
                                p_camera->buf[p_camera->buf_cur_index].frame_info.frame.height,
                                p_camera->buf[p_camera->buf_cur_index].buf.bytesused);
    } else if (p_camera->pixelformat == V4L2_PIX_FMT_NV21) {
    
        
        uvc_player_nv21_to_nv12((vs_char_t *)p_camera->buf[p_camera->buf_cur_index].start,
                                (vs_char_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0],
                                p_camera->buf[p_camera->buf_cur_index].frame_info.frame.width,
                                p_camera->buf[p_camera->buf_cur_index].frame_info.frame.height);
    } else if (p_camera->pixelformat == V4L2_PIX_FMT_NV12) {
    
        
        memcpy((void *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0],
                       p_camera->buf[p_camera->buf_cur_index].start, p_camera->buf[p_camera->buf_cur_index].buf.bytesused);
        //uvc_player_save_stream(p_camera->buf[p_camera->buf_cur_index].start, p_camera->buf[p_camera->buf_cur_index].buf.bytesused, p_camera->pixelformat);
    } else {
    
        
        VS_UVC_LOG_E("unsupport pixelformat[%c%c%c%c].\n", p_camera->pixelformat & 0xFF,
                    (p_camera->pixelformat >> 8) & 0xFF, (p_camera->pixelformat >> 16) & 0xFF, (p_camera->pixelformat >> 24) & 0xFF);
        uvc_framebuf_vb_release(p_camera);
        return ret;
    }
    

    //ret = vs_mal_vpp_grp_frame_send(p_camera->player.vpp_grpid, &p_camera->buf[p_camera->buf_cur_index].frame_info, timeout_ms);
    

    if (0)
    {
        char buf[256] = {0};
        sprintf(buf, "%d.raw", p_camera->buf_cur_index);
        printf("buf:%s\n", buf);
        dumpToFile(buf,(vs_char_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0], 256*192); 
    }



#ifdef UVC_PIC_SEQUENCE 
    // PRINTLINE
    ret = infra_player_procedure(NULL, UVC_WIDTH, (void *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0], 640);
    if (ret != VS_SUCCESS) {
        printf("infra_player_procedure failed, ret[0x%x]\n", ret);
        return ret;
    }
    // PRINTLINE

#endif


    /*VIDEO_ZOOM_ENABLED, enable ive dma copy and ive resize*/
#ifdef VIDEO_ZOOM_ENABLED   

    if (p_camera->zoom_factor > 10 && p_camera->zoom_factor <= 80)
    {

        //STARTCLOCK
        vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
        vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
        vs_uint32_t ive_dst_size;
        vs_ive_dma_cfg_s dma_cfg;
        vs_uint32_t handle = 0;
        vs_bool_t block = VS_TRUE;
        vs_bool_t finish = VS_FALSE;

        /* resize factor, range 1.0 - 8.0 */
        vs_float_t ive_resize_factor = p_camera->zoom_factor/10.0;

        /* image origin size */
        vs_uint32_t ive_image_origin_stride = UVC_WIDTH;
        vs_uint32_t ive_image_origin_width = UVC_WIDTH;
        vs_uint32_t ive_image_origin_height = UVC_HEIGHT;

        vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
        vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
        vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

        //vs_mal_ive_dma input width must be multiple of 32. 
        ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width % 32));
        ive_image_dst_height = ive_image_dst_width * 0.8;// 0.8 scale
        ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
        ive_image_dst_stride =  ive_image_dst_width;

        //start point
        vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2;
        vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2;

        // resize
        vs_uint64_t resize_phys_addr = 0;
        vs_void_t *p_resize_virt_addr = NULL;

        vs_uint32_t resize_with = ive_image_dst_width * ive_resize_factor;
        resize_with = (resize_with % 32) == 0 ? resize_with : resize_with + (32 - (resize_with % 32));
        vs_uint32_t resize_height = resize_with * 0.8;
        resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
        vs_uint32_t resize_stride = resize_with;
        
        vs_ive_image_s ive_image_src;
        vs_ive_image_s ive_image_dst;
        vs_ive_resize_cfg_s resize_cfg;

        resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
        resize_cfg.image_num = 1;
        ive_image_src.type = E_IVE_IMAGE_TYPE_U8C1;
        ive_image_src.stride[0] = ive_image_origin_stride;
        ive_image_src.stride[1] = ive_image_origin_stride;
        ive_image_src.width = ive_image_dst_width;
        ive_image_src.height = ive_image_dst_height;

        
        ive_image_dst.type = E_IVE_IMAGE_TYPE_U8C1;
        ive_image_dst.stride[0] = resize_stride;
        ive_image_dst.stride[1] = resize_stride;
        ive_image_dst.width = resize_with;
        ive_image_dst.height = resize_height;

        vs_uint32_t resize_size = resize_stride * resize_height;
        ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
            goto exit_ive_mmz_free;
        }

        //phy addr mmp virt addr 
        ive_image_src.phys_addr[0] = p_camera->buf[p_camera->buf_cur_index].frame_info.frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
        ive_image_src.virt_addr[0] = (vs_char_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;

        ive_image_dst.phys_addr[0] = resize_phys_addr;
        ive_image_dst.virt_addr[0] = (vs_void_t*)p_resize_virt_addr;


        ret = vs_mal_ive_resize(&handle, &ive_image_src, &ive_image_dst, &resize_cfg, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        }
        ret = vs_mal_ive_query(handle, &finish, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        }


        //copy again
        vs_uint32_t resize_src_x_point = resize_with/2 - ive_image_origin_width/2 ;
        vs_uint32_t resize_src_y_point = resize_height/2 -ive_image_origin_height/2;

        vs_ive_data_s ive_copy_src_data;
        ive_copy_src_data.stride = resize_stride;
        ive_copy_src_data.width = ive_image_origin_width;
        ive_copy_src_data.height = ive_image_origin_height;
        ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
        ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


        vs_ive_data_s ive_copy_dst_data;
        ive_copy_dst_data.stride = ive_image_origin_stride;
        ive_copy_dst_data.width = ive_image_origin_width;
        ive_copy_dst_data.height = ive_image_origin_height;
        ive_copy_dst_data.phys_addr = p_camera->buf[p_camera->buf_cur_index].frame_info.frame.phys_addr[0];
        ive_copy_dst_data.virt_addr = (vs_void_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0];

        dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


        ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
        }
        ret = vs_mal_ive_query(handle, &finish, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
        }
        
exit_ive_mmz_free:

        //free mm
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        //ENDCLOCK("ive copy and resize")
    }
    

#endif
    
#ifdef WORK_WITH_2_MIPI
    vs_video_frame_info_s *p_frame[VII_MAX_VC_NUM];

    p_frame[0] = &p_camera->buf[p_camera->buf_cur_index].frame_info;
    //printf("modid:%d\n",  p_frame[0]->modid );
    p_frame[0]->modid = E_MOD_ID_VII;
    //printf("modid:%d\n",  p_frame[0]->modid );

    ret = vs_mal_vii_pipe_frame_send(VII_DST_PIPE,p_frame , 1, timeout_ms);
#else
    ret = vs_mal_vpp_grp_frame_send(IR_VPP_GRP_ID, &p_camera->buf[p_camera->buf_cur_index].frame_info, timeout_ms);
#endif
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("frame_send to vpp or viipipe failed, ret[0x%x]\n", ret);
        uvc_framebuf_vb_release(p_camera);
        return ret;
    }

    
exit_frc_skp: 
    ret = uvc_framebuf_vb_release(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_framebuf_vb_release failed, ret[0x%x]\n", ret);
        return ret;
    }

    
    return ret;
}

vs_int32_t uvc_player_framesend(vs_void_t* p_camera_info)
{
    vs_int32_t ret = VS_FAILED;
    vs_int32_t timeout_ms = 30;
    vs_uvc_camera_info_s* p_camera = p_camera_info;

    if (p_camera->pixelformat == V4L2_PIX_FMT_H265 || p_camera->pixelformat == V4L2_PIX_FMT_HEVC ||
        p_camera->pixelformat == V4L2_PIX_FMT_H264 || p_camera->pixelformat == V4L2_PIX_FMT_MJPEG) {
        vs_vdec_stream_s stream = {0};

        stream.p_virt_addr = p_camera->buf[p_camera->buf_cur_index].start;
        stream.len = p_camera->buf[p_camera->buf_cur_index].buf.bytesused;
        stream.is_frame_end = VS_FALSE;
        stream.is_stream_end = VS_FALSE;
        stream.is_display = VS_TRUE;
        ret = vs_mal_vdec_stream_send(p_camera->player.vdec_chnid, &stream, timeout_ms);
        if (ret != VS_SUCCESS) {
            VS_UVC_LOG_E("vs_mal_vdec_stream_send failed, ret[0x%x]\n", ret);
        }
        //uvc_player_save_stream((vs_char_t *)stream.p_virt_addr, stream.len, p_camera->pixelformat);
    } else if (p_camera->pixelformat == V4L2_PIX_FMT_YUYV || p_camera->pixelformat == V4L2_PIX_FMT_NV12  || p_camera->pixelformat == V4L2_PIX_FMT_NV21) {
        //printf("YUYV-----\n");
        ret = uvc_player_framesend_to_vpp(p_camera);
    } else {
        VS_UVC_LOG_E("pixelformat not support[0x%x]!\n", p_camera->pixelformat);
    }
    return ret;
}

