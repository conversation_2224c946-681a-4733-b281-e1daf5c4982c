/**
 * @file    uvc_framebuf.c
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <asm/ioctl.h>
#include <sys/mman.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <unistd.h>

#include "uvc_framebuf.h"
#include "uvc_camera.h"
#include "uvc_util.h"

#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"

static vs_uint32_t g_frame_id = 0;   ///< frame serial number, value range >= 0
static vs_uint64_t g_frame_pts = 0;  ///< time stamp, value range >= 0

vs_pixel_format_e uvc_framebuf_get_pixel_format_by_v4l2_fix_fmt(vs_uint32_t pixelformat)
{
    vs_pixel_format_e  vs_pixel_format = E_PIXEL_FORMAT_YVU_420SP;

    if (pixelformat == V4L2_PIX_FMT_H264) {
        vs_pixel_format = E_PIXEL_FORMAT_YVU_420SP;
    } else if (pixelformat == V4L2_PIX_FMT_H265 || pixelformat == V4L2_PIX_FMT_HEVC) {
        vs_pixel_format = E_PIXEL_FORMAT_YVU_420SP;
    } else if (pixelformat == V4L2_PIX_FMT_MJPEG) {
        vs_pixel_format = E_PIXEL_FORMAT_YVU_420SP;
    } else if (pixelformat == V4L2_PIX_FMT_YUYV) {
        vs_pixel_format = E_PIXEL_FORMAT_YUYV_422;
    } else if (pixelformat == V4L2_PIX_FMT_NV21) {
        vs_pixel_format = E_PIXEL_FORMAT_YVU_420SP;
    } else if (pixelformat == V4L2_PIX_FMT_NV12) {
        vs_pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    }

    return vs_pixel_format;
}

static vs_uint32_t uvc_get_aligned_byte_stride(vs_int32_t width, vs_int32_t input_format, vs_uint32_t *p_luma_stride,
                    vs_uint32_t *p_chroma_stride, vs_uint32_t input_alignment)
{
	vs_uint32_t alignment = input_alignment == 0 ? 1 : input_alignment;
	vs_uint32_t pixelByte = 1;

	if (p_luma_stride == VS_NULL || p_chroma_stride == VS_NULL)
		return 1;

	switch (input_format) {
    case E_PIXEL_FORMAT_YVU_420P: //VCENC_YVU420_PLANAR
		*p_luma_stride = STRIDE_UP(width,alignment);
		*p_chroma_stride = STRIDE_UP(width/2,alignment);
		break;
    case E_PIXEL_FORMAT_YVU_420SP://VCENC_YUV420_SEMIPLANAR
    case E_PIXEL_FORMAT_YUV_420SP://VCENC_YUV420_SEMIPLANAR_VU
		*p_luma_stride = STRIDE_UP(width,alignment);
		*p_chroma_stride = STRIDE_UP(width,alignment);
		break;
	case E_PIXEL_FORMAT_YUV_400://
		*p_luma_stride = STRIDE_UP(width,alignment);
		*p_chroma_stride = 0;
		break;
    case E_PIXEL_FORMAT_YUYV_422://
		*p_luma_stride = STRIDE_UP(width * 2,alignment);
		*p_chroma_stride = 0;
		break;
    default:
		*p_luma_stride = 0;
		*p_chroma_stride = 0;
        pixelByte = 0;
		break;
    }
//  VS_UVC_LOG_E("luma_stride[%u] chroma_stride[%u] \n", *p_luma_stride, *p_chroma_stride);

  return pixelByte;
}

static vs_uint32_t uvc_get_aligned_picsize_by_format(vs_pixel_format_e type, vs_uint32_t width, vs_uint32_t height)
{
	vs_uint32_t luma_stride = 0, chroma_stride = 0, alignment = 0;
	vs_uint32_t luma_size = 0, chroma_size = 0, frame_size = 0;

	switch(type) {
    case E_PIXEL_FORMAT_YVU_420P:
		uvc_get_aligned_byte_stride(width, E_PIXEL_FORMAT_YVU_420P, &luma_stride, &chroma_stride, alignment);
		luma_size = luma_stride * height;
		chroma_size = chroma_stride * height/2*2;
		break;
    case E_PIXEL_FORMAT_YVU_420SP:
    case E_PIXEL_FORMAT_YUV_420SP:
		uvc_get_aligned_byte_stride(width, E_PIXEL_FORMAT_YUV_420SP, &luma_stride, &chroma_stride, alignment);
		luma_size = luma_stride * height;
		chroma_size = chroma_stride * height/2;
		break;
    case E_PIXEL_FORMAT_YUV_400:
		uvc_get_aligned_byte_stride(width, E_PIXEL_FORMAT_YUV_400, &luma_stride, &chroma_stride, alignment);
		luma_size = luma_stride * height;
		chroma_size = 0;
		break;
    case E_PIXEL_FORMAT_YUYV_422:
        uvc_get_aligned_byte_stride(width, E_PIXEL_FORMAT_YUYV_422, &luma_stride, &chroma_stride, alignment);
		luma_size = luma_stride * height;
		chroma_size = 0;
		break;
    default:
		vs_sample_trace("not support this format\n");
		chroma_size = luma_size = 0;
		break;
	}

	frame_size = luma_size + chroma_size;
//	vs_sample_trace("luma_size[%u] chroma_size[%u] frame_size[%u]\n", luma_size, chroma_size, frame_size);
    return frame_size;
}

vs_int32_t uvc_user_vb_pool_init(vs_size_s frame_size, vs_pixel_format_e format, VB_POOL *p_vb_poolid)
{
    struct vs_vb_pool_cfg vb_pool_cfg;
    vs_uint32_t align_size = 0;
    vs_uint32_t alignment = 4096;

    memset(&vb_pool_cfg, 0, sizeof(struct vs_vb_pool_cfg));

    align_size = uvc_get_aligned_picsize_by_format(format, frame_size.width, frame_size.height);
    align_size = STRIDE_UP(align_size, alignment);
    vb_pool_cfg.blk_size = align_size;

    vb_pool_cfg.blk_cnt = 10;
    vb_pool_cfg.remap_mode = VB_REMAP_MODE_NONE;
    *p_vb_poolid = vs_mal_vb_pool_create(&vb_pool_cfg);
    if (*p_vb_poolid == VS_INVALID_POOLID) {
        vs_sample_trace("width[%d] height[%d] vs_mal_vb_pool_create error\n", frame_size.width, frame_size.height);
        return VS_FAILED;
    }
    vs_sample_trace("vb_pool_init vb_poolid[%u] \n", *p_vb_poolid);
    return VS_SUCCESS;
}

vs_int32_t uvc_user_vb_pool_deinit(VB_POOL vb_poolid)
{
    vs_int32_t ret = VS_SUCCESS;
    ret = vs_mal_vb_pool_destory(vb_poolid);
    if(ret != VS_SUCCESS) {
        vs_sample_trace("vb_poolid[%d] ret[0x%x] vs_mal_vb_pool_destory error\n", vb_poolid, ret);
        return ret;
    }
    vs_sample_trace("vb_pool_deinit vb_poolid[%u] \n", vb_poolid);
    return VS_SUCCESS;
}

vs_int32_t uvc_framebuf_vb_acquire(vs_void_t *p_camera_info)
{
    vs_uvc_camera_info_s *p_camera = p_camera_info;
    vs_video_frame_s *p_frame = &p_camera->buf[p_camera->buf_cur_index].frame_info.frame;
    vs_uint32_t blk_size = p_camera->buf_max_size;
    vs_uint32_t luma_stride = 0, chroma_stride = 0;
    VB_BLK vb_blk = VS_NULL;

    p_camera->buf[p_camera->buf_cur_index].frame_info.modid = E_MOD_ID_USER;
    p_camera->buf[p_camera->buf_cur_index].frame_info.poolid = p_camera->buf_poolid;
    p_frame->width = p_camera->width;
    p_frame->height = p_camera->height;
    p_frame->pixel_format = E_PIXEL_FORMAT_YUV_420SP;//uvc_framebuf_get_pixel_format_by_v4l2_fix_fmt(p_camera->pixelformat);

    vb_blk = VS_INVALID_VB_HANDLE;
    //blk_size = STRIDE_UP(blk_size, 4096);
    if (VS_INVALID_VB_HANDLE == (vb_blk = vs_mal_vb_block_get(p_camera->buf_poolid, blk_size, VS_NULL))) {
        VS_UVC_LOG_E("get blk_size[%u] of block pool[%u] failed.\n", blk_size, p_camera->buf_poolid);
        return VS_FAILED;
    }

    if (0 == (p_frame->phys_addr[0] = vs_mal_vb_handle2physaddr(vb_blk))) {
        VS_UVC_LOG_E("get phys of block[%u] failed.\n", vb_blk);
        vs_mal_vb_block_release(vb_blk);
        return VS_FAILED;
    }
//    VS_UVC_LOG_I("get phys[%llu][%llx] of block[%u] ok.\n", p_frame->phys_addr[0], p_frame->phys_addr[0], blk);
    if (0 == (p_frame->virt_addr[0] = (vs_uint64_t)vs_mal_sys_mmap(p_frame->phys_addr[0], blk_size))) {
        VS_UVC_LOG_E("get map phys[%llu] blk_size[%u] to virt failed.\n", p_frame->phys_addr[0], blk_size);
        vs_mal_vb_block_release(vb_blk);
        return VS_FAILED;
    }
    uvc_get_aligned_byte_stride(p_camera->width, p_frame->pixel_format, &luma_stride, &chroma_stride, 64);
    p_frame->stride[0] = luma_stride;
    p_frame->stride[1] = 0;
    p_frame->stride[2] = 0;
    p_frame->virt_addr[1] = 0;
    p_frame->virt_addr[2] = 0;
    p_frame->phys_addr[1] = 0;
    p_frame->phys_addr[2] = 0;
    p_frame->compress_header_virt_addr[0] = 0;
    p_frame->compress_header_virt_addr[1] = 0;
    p_frame->compress_header_virt_addr[2] = 0;
    p_frame->compress_header_phys_addr[0] = 0;
    p_frame->compress_header_phys_addr[1] = 0;
    p_frame->compress_header_phys_addr[2] = 0;
    g_frame_id += 2;
    g_frame_pts += 1000 / p_camera->fps;
    p_frame->pts += g_frame_pts;
    p_frame->id += g_frame_id;

    if (E_PIXEL_FORMAT_YUV_400 != p_frame->pixel_format) {
        p_frame->virt_addr[1] = p_frame->virt_addr[0] + luma_stride * p_camera->height;
        p_frame->virt_addr[2] = p_frame->virt_addr[0] + luma_stride * p_camera->height;
        p_frame->phys_addr[1] = p_frame->phys_addr[0] + luma_stride * p_camera->height;
        p_frame->phys_addr[2] = p_frame->phys_addr[0] + luma_stride * p_camera->height;
        p_frame->stride[1] = chroma_stride;
        p_frame->stride[2] = chroma_stride;
    }

    return VS_SUCCESS;
}

vs_int32_t uvc_framebuf_vb_release(vs_void_t *p_camera_info)
{
	VB_BLK blk = VS_INVALID_VB_HANDLE;
    vs_uvc_camera_info_s* p_camera = p_camera_info;
    vs_video_frame_s *p_frame = &p_camera->buf[p_camera->buf_cur_index].frame_info.frame;;
    vs_uint32_t blk_size = p_camera->buf_max_size;;


	vs_mal_sys_unmap((vs_void_t*)p_frame->virt_addr[0], blk_size);
	blk = vs_mal_vb_physaddr2handle(p_frame->phys_addr[0]);
	if (VS_INVALID_VB_HANDLE != blk) {
		vs_mal_vb_block_release(blk);
	} else {
		VS_UVC_LOG_E("can't get block handle for phys[0x%llx] failed.\n", p_frame->phys_addr[0]);
		return VS_FAILED;
	}

	return VS_SUCCESS;
}

vs_int32_t uvc_framebuf_init(vs_void_t* p_camera_info)
{
    vs_uint32_t i = 0, j = 0;
    vs_int32_t ret;
    struct v4l2_buffer buffer;

    struct v4l2_requestbuffers req;
    vs_size_s frame_size;
    vs_pixel_format_e  vs_pixel_format;
    vs_uvc_camera_info_s* p_camera = p_camera_info;

    memset(&req, 0, sizeof(req));
	req.count = p_camera->buf_count;
	req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
	req.memory = p_camera->mem_type;
	ret = ioctl(p_camera->fd, VIDIOC_REQBUFS, &req);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_framebuf_init failed:(0x%x).\n", ret);
        return VS_FAILED;
    }
    p_camera->buf_count = req.count;
    p_camera->buf = calloc(req.count, sizeof(vs_uvc_framebuffer_s));

    frame_size.height = p_camera->height;
    frame_size.width = p_camera->width;
    vs_pixel_format = uvc_framebuf_get_pixel_format_by_v4l2_fix_fmt(p_camera->pixelformat); //E_PIXEL_FORMAT_YUYV_422;
    p_camera->buf_max_size = uvc_get_aligned_picsize_by_format(vs_pixel_format, frame_size.width, frame_size.height);
    p_camera->buf_max_size = STRIDE_UP(p_camera->buf_max_size, 4096);
    VS_UVC_LOG_I("p_camera->buf_max_sizet[%u]\n", p_camera->buf_max_size);

    ret = uvc_user_vb_pool_init(frame_size, vs_pixel_format, &p_camera->buf_poolid);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_user_vb_pool_init failed, ret[0x%x]\n", ret);
        goto uvc_framebuf_init_1;
    }

    
    for (i = 0; i < p_camera->buf_count; i++) {
        
		memset(&buffer, 0, sizeof(buffer));

		buffer.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
		buffer.memory = p_camera->mem_type;
		buffer.index = i;

        
		ret = ioctl(p_camera->fd, VIDIOC_QUERYBUF, &buffer);
        if (ret < 0) {
            VS_UVC_LOG_E("UVC: VIDIOC_QUERYBUF failed: %s (%d).\n", strerror(errno), errno);
            goto uvc_framebuf_init_3;
        }
        
		if (buffer.length > p_camera->buf_max_size) {
			p_camera->buf_max_size = buffer.length;
        }
        

		VS_UVC_LOG_I("count:%d, length:%d\n", i, buffer.length);

        if (p_camera->mem_type == V4L2_MEMORY_MMAP) {
            
            p_camera->buf[i].buf = buffer;
    		p_camera->buf[i].length = buffer.length;
    		p_camera->buf[i].start = mmap(NULL, buffer.length, PROT_READ | PROT_WRITE, MAP_SHARED, p_camera->fd, buffer.m.offset);
    		if (NULL == p_camera->buf[i].start) {
    			perror("mmap error");
    			goto uvc_framebuf_init_3;
    		}
            
        }else if (p_camera->mem_type == V4L2_MEMORY_USERPTR) {
            
            vs_uint32_t page_size = getpagesize(), payload_size = p_camera->buf_max_size;

            
            payload_size = (payload_size + page_size - 1) & ~(page_size - 1);
            p_camera->buf[i].buf = buffer;
            p_camera->buf[i].length = payload_size;;
#if 0
            ret = posix_memalign(&p_camera->buf[i].start, page_size, payload_size); //malloc(payload_size);
            if (ret < 0) {
                VS_UVC_LOG_E("UVC: Out of memory, memalign error!\n");
                ret = -ENOMEM;
                goto uvc_framebuf_init_3;
            }
#endif
            p_camera->buf[i].start = malloc(payload_size); //malloc(payload_size);
            if (!p_camera->buf[i].start) {
                VS_UVC_LOG_E("UVC: Out of memory, malloc error!\n");
                ret = -ENOMEM;
                goto uvc_framebuf_init_3;
            }
            
            p_camera->buf[i].buf.m.userptr = (unsigned long)p_camera->buf[i].start;
            VS_UVC_LOG_I("UVC: Buffer index(%d) malloc at address[%p] length[%u] page_size[%u].\n",
                    i, p_camera->buf[i].start, p_camera->buf[i].length, page_size);
        }
	}

    
    for (i = 0; i < p_camera->buf_count; i++) {
		memset(&buffer, 0, sizeof(buffer));

		buffer.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
		buffer.memory = p_camera->mem_type;
		buffer.index = i;

		ret = ioctl(p_camera->fd, VIDIOC_QBUF, &buffer);
        if (ret < 0) {
            VS_UVC_LOG_E("UVC: VIDIOC_QBUF failed: %s (%d).\n", strerror(errno), errno);
            i = p_camera->buf_count;
            goto uvc_framebuf_init_2;
        }
        
	}

    return VS_SUCCESS;
uvc_framebuf_init_3:
    for (j = 0; j < i; j++) {
        if (p_camera->mem_type == V4L2_MEMORY_MMAP) {
            munmap(p_camera->buf[j].start, p_camera->buf[j].length);
        } else if (p_camera->mem_type == V4L2_MEMORY_USERPTR) {
            free(p_camera->buf[j].start);
        }
    }
uvc_framebuf_init_2:
    ret = uvc_user_vb_pool_deinit(p_camera->buf_poolid);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_user_vb_pool_deinit failed, ret[0x%x]\n", ret);
    }
uvc_framebuf_init_1:
    free(p_camera->buf);

    return VS_FAILED;
}

vs_int32_t uvc_framebuf_deinit(vs_void_t* p_camera_info)
{
	vs_uint32_t i = 0, ret;
    vs_uvc_camera_info_s* p_camera = p_camera_info;
    for (i = 0; i < p_camera->buf_count;  i++) {
        if (p_camera->mem_type == V4L2_MEMORY_MMAP) {
            munmap(p_camera->buf[i].start, p_camera->buf[i].length);
        } else if (p_camera->mem_type == V4L2_MEMORY_USERPTR) {
            free(p_camera->buf[i].start);
        }
    }

    ret = uvc_user_vb_pool_deinit(p_camera->buf_poolid);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("uvc_user_vb_pool_deinit failed, ret[0x%x]\n", ret);
        //return ret;
    }

	free(p_camera->buf);
	p_camera->buf_count = 0;
	p_camera->buf = NULL;

//	free(p_camera->head.start);
//	p_camera->head.length = 0;
//	p_camera->head.start = NULL;

	return VS_SUCCESS;
}

vs_int32_t uvc_framebuf_dqbuf(vs_void_t* p_camera_info)
{
    vs_int32_t ret;
	struct v4l2_buffer buffer;
    vs_uvc_camera_info_s* p_camera = p_camera_info;

	memset(&buffer, 0, sizeof(buffer));

	buffer.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
	buffer.memory = p_camera->mem_type;
    p_camera->buf_cur_index = -1;

	ret = ioctl(p_camera->fd, VIDIOC_DQBUF, &buffer);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_DQBUF failed: %s (%d).\n", strerror(errno), errno);
        return ret;
    }

    p_camera->buf_cur_index = buffer.index;
    if (p_camera->buf[p_camera->buf_cur_index].status != E_UVC_FRAMEBUF_STATUS_RELEASE) {
        VS_UVC_LOG_E("index(%d) should be E_UVC_FRAMEBUF_STATUS_RELEASE, error.\n", p_camera->buf_cur_index);
    }
    p_camera->buf[p_camera->buf_cur_index].buf = buffer;
    p_camera->buf[p_camera->buf_cur_index].status = E_UVC_FRAMEBUF_STATUS_ACQUIRE;

    //VS_UVC_LOG_D("index(%d) uvc_framebuf_dqbuf ok.\n", p_camera->buf_cur_index);
    return VS_SUCCESS;
}

vs_int32_t uvc_framebuf_qbuf(vs_void_t* p_camera_info)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_uvc_camera_info_s* p_camera = p_camera_info;

    ret = ioctl(p_camera->fd, VIDIOC_QBUF, &p_camera->buf[p_camera->buf_cur_index].buf);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_QBUF failed: %s (%d).\n", strerror(errno), errno);
    } else {
        p_camera->buf[p_camera->buf_cur_index].status = E_UVC_FRAMEBUF_STATUS_RELEASE;
    }

    //VS_UVC_LOG_D("index(%d) uvc_framebuf_qbuf ok.\n", p_camera->buf_cur_index);
	return ret;
}

