/**
 * @file    uvc_camera.h
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#ifndef _VS_UVC_CAMERA_H__
#define _VS_UVC_CAMERA_H__
#include "vs_type.h"
#include "uvc_camera.h"
#include "uvc_player.h"
#include "uvc_framebuf.h"

#ifdef __cplusplus
extern "C" {
#endif

//#define UVC_DEVICE_PATH         "/dev/video0"
#define UVC_V4L2_FMX_MAX            128

#define UVC_V4L2_FMTDESC_MAX        128
#define UVC_V4L2_FRMSIZE_MAX        128
#define UVC_V4L2_FRMIVAL_MAX        128




typedef struct vs_uvc_capability_info {
    struct v4l2_fmtdesc          vfmtdesc[UVC_V4L2_FMTDESC_MAX];
    vs_int32_t                   vfmtdesc_num;
    //struct v4l2_fmtdesc         vfmtdesc[UVC_V4L2_FMX_MAX][UVC_V4L2_FMTDESC_MAX];
    //vs_int32_t                  vfmtdesc_num;
    struct v4l2_frmsizeenum     vfrmsize[UVC_V4L2_FMX_MAX][UVC_V4L2_FRMSIZE_MAX];;
    vs_int32_t                  vfrmsize_num;
    struct v4l2_frmivalenum     vfrmival[UVC_V4L2_FMX_MAX][UVC_V4L2_FRMSIZE_MAX][UVC_V4L2_FRMIVAL_MAX];
    vs_int32_t                  vfrmival_num;
} vs_uvc_capability_info_s;

typedef struct vs_uvc_camera_info {
    vs_char_t                   dev_path[256];
    vs_int32_t                  fd;
    vs_uint32_t                 width;
    vs_uint32_t                 height;
    vs_uint32_t                 fps;
    vs_uint32_t                 pixelformat;
    vs_uint32_t                 mem_type;

    pthread_t                   uvc_camera_td;
    vs_uint32_t                 is_stop_uvc_camera;

    VB_POOL                     buf_poolid;
    vs_uint32_t                 buf_count;
    vs_uvc_framebuffer_s        *buf;
    vs_int32_t                  buf_cur_index;
    vs_uint32_t                 buf_max_size;

    vs_uvc_player_info_s        player;

    struct v4l2_capability      vcap;
    struct v4l2_format          cur_vfmt;
    vs_uvc_capability_info_s    *p_capability;
#ifdef VIDEO_ZOOM_ENABLED
    //todo, should be float.
    vs_uint32_t                 zoom_factor;
#endif
} vs_uvc_camera_info_s;

vs_int32_t uvc_camera_open(vs_uvc_camera_info_s *p_camera);
vs_int32_t uvc_camera_init(vs_uvc_camera_info_s *p_camera);
vs_int32_t uvc_camera_start(vs_uvc_camera_info_s *p_camera);
vs_void_t * uvc_camera_display(vs_void_t *p_param);
vs_int32_t uvc_camera_stop(vs_uvc_camera_info_s *p_camera);
vs_void_t uvc_camera_deinit(vs_uvc_camera_info_s *p_camera);
vs_void_t uvc_camera_close(vs_uvc_camera_info_s *p_camera);

#ifdef __cplusplus
}
#endif

#endif /*_VS_UVC_CAMERA_H__*/
