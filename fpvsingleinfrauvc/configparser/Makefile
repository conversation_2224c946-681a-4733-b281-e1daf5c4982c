#
# @file    Makefile
# <AUTHOR> Software Group
# @date    2021-11-09
# @version v1.0
# @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
#



PROJECT_HOME = ./

MP_CFLAGS += -DVS_CARINA

CC=aarch64-linux-gnu-gcc
CXX=aarch64-linux-gnu-g++
LINK_TARGET = libconfigparser.so
SRC_DIR = ./src
OUTBIN_DIR = ./bin/
OUTLIB_DIR = ./lib/

SOURCES = $(wildcard $(SRC_DIR)/*.c)
OBJS = $(SOURCES:.c=.o)

####
#CFLAGS = -O3 -mfpu=neon -mthumb -march=armv7-a -funsafe-math-optimizations -mfloat-abi=softfp

####

#CFLAGS += -fopenmp
#CFLAGS += -Wall -Ofast -funsafe-math-optimizations -c -Wno-unused-function -fstack-protector-strong -fPIC -c

SHARE   := -fPIC -shared -o

CFLAGS += -Wall -Ofast -c -Wno-unused-function -fstack-protector-strong -fPIC -c
CFLAGS += -I ./include/

#LDFLAGS += -L/opt/opencv/lib/ -lopencv_world  -lpthread -lm -lstdc++ -ldl 
LDFLAGS += -lpthread -lm -lstdc++ -ldl 

REBUILDABLES = $(OBJS) $(LINK_TARGET)

.PHONY: all
all : $(LINK_TARGET)
	@echo $(LINK_TARGET) compile done!

$(LINK_TARGET) : $(OBJS)
	$(CC) $(OBJS) $(LDFLAGS) $(SHARE) $@
	@mkdir -p $(OUTLIB_DIR) && mv -f $@ $(OUTLIB_DIR)

$(OBJS):%.o:%.c
	$(CC) $(CFLAGS) -c -o $@ $<

clean :
	@rm -f $(REBUILDABLES)
	@rm -f $(OUTLIB_DIR)/*
	@echo $(LINK_TARGET) Clean done!
