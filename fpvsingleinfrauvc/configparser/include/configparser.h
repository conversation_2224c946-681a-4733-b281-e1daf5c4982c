#pragma once

extern "C" 
{
//char *GetIniKeyString(char *title,char *key,char *filename);
char *GetIniKeyString(char *title,char *key,char *filename,char *strbuf, unsigned int buflen);
int GetIniKeyInt(char *title,char *key,char *filename);
float GetIniKeyFloat(char *title, char *key, char *filename);
int PutIniKeyString(char *title,char *key,char *val,char *filename);
int PutIniKeyInt(char *title,char *key,int val,char *filename);
};
