#ifndef VIDEO_ZOOM_MESSAGE_H_
#define VIDEO_ZOOM_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"
void airvisen_start_video_zoom_ipc();
void airvisen_stop_video_zoom_ipc();
void airvisen_start_video_zoom_client_ipc();
void airvisen_start_video_zoom_command(int zoom_factor);
int dispatch_video_zoom_message(int data_len, char *data);
int airvisen_recv_video_zoom_message(char *data, int data_len);

#ifdef __cplusplus
}
#endif

#endif /*VIDEO_ZOOM_MESSAGE_H_*/