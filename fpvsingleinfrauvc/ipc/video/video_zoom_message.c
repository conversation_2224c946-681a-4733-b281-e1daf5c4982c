#include "ipc.h"
#include "video_record_message.h"


static int sock;
#define VIDEO_ZOOM_SOCKET_ADDRESS "ipc:///tmp/airvisen.videozoom.ipc"

void airvisen_start_video_zoom_ipc()
{
    sock = ipc_server_start(VIDEO_ZOOM_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}


void airvisen_start_video_zoom_client_ipc()
{
    sock = ipc_client_start(VIDEO_ZOOM_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    
}

void airvisen_start_video_zoom_command(int zoom_factor)
{
    airvisen_track_message_video_zoom_s video_zoom_msg = {0};
    video_zoom_msg.head.cmd = VIDEO_ZOOM;
    video_zoom_msg.head.len = sizeof(airvisen_track_message_video_zoom_s);
    video_zoom_msg.factor = zoom_factor;
    ipc_send_message(sock, sizeof(airvisen_track_message_video_zoom_s), (char *)&video_zoom_msg);
}


void airvisen_stop_video_zoom_ipc()
{
    ipc_close(sock);
}


int dispatch_video_zoom_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case VIDEO_ZOOM:{
            printf("Recv VIDEO ZOOM command\n");
            return START_VIDEO_RECORD;
        }
        break;

    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
        break;
    }
}


int airvisen_recv_video_zoom_message(char *data, int data_len)
{
    return ipc_recv_message_wait(sock, data, data_len);
}

