#ifndef VIDEO_RECORD_MESSAGE_H_
#define VIDEO_RECORD_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"
void airvisen_start_video_record_ipc();
void airvisen_stop_video_record_ipc();
void airvisen_start_video_record_client_ipc();

void airvisen_send_start_video_record_command(int venc_chn);
void airvisen_send_stop_video_record_command(int venc_chn);
void airvisen_notify_video_record_stop_event(int stop_reason);
int airvisen_recv_video_record_message(char *data, int data_len, int flags);

#ifdef __cplusplus
}
#endif

#endif /*VIDEO_RECORD_MESSAGE_H_*/