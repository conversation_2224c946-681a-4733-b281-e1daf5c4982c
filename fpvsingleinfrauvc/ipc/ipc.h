#ifndef IPC_H_INCLUDED
#define IPC_H_INCLUDED

#ifdef __cplusplus
extern "C" {
#endif

#define NN_IPC -2

/* The object set here must be valid as long as you are using the socket */
#define NN_IPC_SEC_ATTR 1
#define NN_IPC_OUTBUFSZ 2
#define NN_IPC_INBUFSZ 3

int ipc_send_message (int sock, int data_len, char *data);
int ipc_recv_message(int sock, char *data, int data_len, int flags);
int ipc_recv_message_wait(int sock, char *data, int data_len);
int ipc_server_start(char *address);
int ipc_client_start(char *address);
void ipc_close(int sock);

#ifdef __cplusplus
}
#endif


#endif

