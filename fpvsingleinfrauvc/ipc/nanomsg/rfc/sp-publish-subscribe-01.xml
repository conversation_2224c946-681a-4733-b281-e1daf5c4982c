<?xml version="1.0" encoding="US-ASCII"?>
<!DOCTYPE rfc SYSTEM "rfc2629.dtd">

<rfc category="info" docName="sp-publish-subscribe-01">

  <front>

    <title abbrev="Publish/Subscribe SP">
    Publish/Subscribe Scalability Protocol
    </title>

    <author fullname="<PERSON> Sustrik" initials="<PERSON><PERSON>" role="editor"
            surname="<PERSON><PERSON><PERSON>">
      <address>
        <email><EMAIL></email>
      </address>
    </author>

    <date month="May" year="2014" />

    <area>Applications</area>
    <workgroup>Internet Engineering Task Force</workgroup>

    <keyword>Publish</keyword>
    <keyword>Subscribe</keyword>
    <keyword>PUB</keyword>
    <keyword>SUB</keyword>
    <keyword>distribution</keyword>
    <keyword>SP</keyword>

    <abstract>
      <t>This document defines a scalability protocol used for distributing
         data to arbitrary number of subscriber nodes.</t>
    </abstract>

  </front>

  <middle>

    <section title = "Introduction">

      <t>Blah-blah.</t>

    </section>

    <section title = "Underlying protocol">

      <t>The publish/subscribe protocol can be run on top of any SP mapping,
         such as, for example, <xref target='SPoverTCP'>SP TCPmapping</xref>.
         </t>

      <t>Also, given that SP protocols describe the behaviour of entire
         arbitrarily complex topology rather than of a single node-to-node
         communication, several underlying protocols can be used in parallel.
         For example, publisher can send a message to intermediary node via TCP.
         The intermediate node can then forward the message via PGM etc.</t>

      <figure>
        <artwork>
+---+     TCP    +---+    PGM    +---+
|   |----------->|   |---------->|   |
+---+            +---+           +---+
                   |
                   |      PGM    +---+
                   +------------>|   |
                                 +---+
        </artwork>
      </figure>

    </section>

    <section title = "Overview of the algorithm">
      <t>Blah-blah.</t>
    </section>

    <section title = "Hop-by-hop vs. End-to-end">
      <t>Blah-blah.</t>
    </section>

    <section title = "Hop-by-hop functionality">

      <section title = "PUB endpoint">
        <t>Blah-blah.</t>
      </section>

      <section title = "SUB endpoint">
        <t>Blah-blah.</t>
      </section>
   
    </section>

    <section title = "End-to-end functionality">

      <t>End-to-end functionality is built on top of hop-to-hop functionality.
         Thus, an endpoint on the edge of a topology contains all the
         hop-by-hop functionality, but also implements additional
         functionality of its own. This end-to-end functionality acts
         basically as a user of the underlying hop-by-hop functionality.</t>

      <section title = "PUB endpoint">
        <t>Blah-blah.</t>
      </section>

      <section title = "SUB endpoint">
        <t>Blah-blah.</t>
      </section>

    </section>

    <section title = "Loop avoidance">
      <t>TODO: Do we want any loop avoidance in PUB/SUB?</t>
    </section>

    <section anchor="IANA" title="IANA Considerations">
      <t>New SP endpoint types PUB and SUB should be registered by IANA. For
         now, value of 32 should be used for PUB endpoints and value of 33 for
         SUB endpoints.</t>

      <t>IANA should eventually also register and issue numbers for different
         message matching algorithms.</t>
    </section>

    <section anchor="Security" title="Security Considerations">
      <t>The mapping is not intended to provide any additional security to the
         underlying protocol. DoS concerns are addressed within
         the specification.</t>
    </section>

  </middle>

  <back>
    <references>
      <reference anchor='SPoverTCP'>
         <front>
           <title>TCP mapping for SPs</title>
           <author initials='M.' surname='Sustrik' fullname='M. Sustrik'/>
           <date month='August' year='2013'/>
         </front>
         <format type='TXT' target='sp-tcp-mapping-01.txt'/>
       </reference>
    </references>
  </back>

</rfc>

