<?xml version="1.0" encoding="US-ASCII"?>
<!DOCTYPE rfc SYSTEM "rfc2629.dtd">

<rfc category="info" docName="sp-websocket-mapping-01">

  <front>

    <title abbrev="WebSocket mapping for SPs">
    WebSocket Mapping for Scalability Protocols
    </title>

    <author fullname="<PERSON>" initials="<PERSON><PERSON>" role="editor"
            surname="<PERSON><PERSON><PERSON>">
      <address>
        <email><EMAIL></email>
      </address>
    </author>
    <author fullname="<PERSON>" initials="G."
            surname="<PERSON><PERSON><PERSON><PERSON>">
      <address>
        <email><EMAIL></email>
      </address>
    </author>

    <date month="February" year="2015" />

    <area>Applications</area>
    <workgroup>Internet Engineering Task Force</workgroup>

    <keyword>WebSocket</keyword>
    <keyword>SP</keyword>

    <abstract>
      <t>This document defines the WebSocket mapping for scalability
         protocols.</t>
    </abstract>

  </front>

  <middle>

    <section title = "Underlying protocol">

      <t>This mapping should be layered on the top of WebSocket protocol
         as defined in RFC 6455.</t>

    </section>

    <section title = "Connection initiation">

      <t>Standard WebSocket handshake is done following RFC 6455.</t>

      <t>The Sec-WebSocket-Protocol field
         MUST be set in the client's HTTP request to match the protocol used
         by the WebSocket server.  Accordingly, the following values should
         be used.</t>

      <t>pair.sp.nanomsg.org (NN_PAIR client and server)</t>
      <t>req.sp.nanomsg.org (NN_REQ server, NN_REP client)</t>
      <t>rep.sp.nanomsg.org (NN_REP server, NN_REQ client)</t>
      <t>pub.sp.nanomsg.org (NN_PUB server, NN_SUB client)</t>
      <t>sub.sp.nanomsg.org (NN_SUB server, NN_PUB client)</t>
      <t>surveyor.sp.nanomsg.org (NN_SURVEYOR server, NN_RESPONDENT client)</t>
      <t>respondent.sp.nanomsg.org (NN_RESPONDENT server, NN_SURVEYOR client)</t>
      <t>push.sp.nanomsg.org (NN_PUSH server, NN_PULL client)</t>
      <t>pull.sp.nanomsg.org (NN_PULL server, NN_PUSH client)</t>
      <t>bus.sp.nanomsg.org (NN_BUS client and server)</t>

      <t>If the server supports the requested SP protocol as indicated
         by the Sec-WebSocket-Protocol header, then it MUST respond with
         the same Sec-WebSocket-Protocol value sent by the client.</t>
     
      <t>If the server does support the requested SP protocol, then it MUST
         fail the WebSocket connection using the Close code of 1002, as
         specified in RFC 6455.</t>

      <t>For example, an NN_REQ client socket, connecting to
         an NN_REP server, will send the value rep.sp.nanomsg.org in
         the Sec-WebSocket-Protocol field.  The NN_REP socket on the
         server would then include the same rep.sp.nanomsg.org in its
         response.</t>
  
    </section>

    <section title = "Message">

      <t>SP message maps directly to WebSocket message. The message can be
         fragmented as needed. Frame boundaries are ignored by the SP
         layer.</t>

      <t>When possible, binary frames SHOULD be used in preference to
         text frames.  If text frames are in use, then SP message payloads
         MUST be comprised of legal UTF-8 text only.</t>

    </section>

    <section anchor="IANA" title="IANA Considerations">
      <t>This memo includes no request to IANA.</t>
    </section>

    <section anchor="Security" title="Security Considerations">
      <t>The mapping isn't intended to provide any additional security in
         addition to what WebSocket does. DoS concerns are addressed within
         the specification.</t>
    </section>

  </middle>

</rfc>
