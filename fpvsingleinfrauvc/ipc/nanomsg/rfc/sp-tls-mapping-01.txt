



Internet Engineering Task Force                          G. <PERSON>, Ed.
Internet-Draft
Intended status: Informational                            March 27, 2014
Expires: September 28, 2014


                 TLS Mapping for Scalability Protocols
                           sp-tls-mapping-01

Abstract

   This document defines the mapping for scalability protocols (SP)
   running on top of Transport Layer Security (TLS) v1.2 on top of TCP.

Status of This Memo

   This Internet-Draft is submitted in full conformance with the
   provisions of BCP 78 and BCP 79.

   Internet-Drafts are working documents of the Internet Engineering
   Task Force (IETF).  Note that other groups may also distribute
   working documents as Internet-Drafts.  The list of current Internet-
   Drafts is at http://datatracker.ietf.org/drafts/current/.

   Internet-Drafts are draft documents valid for a maximum of six months
   and may be updated, replaced, or obsoleted by other documents at any
   time.  It is inappropriate to use Internet-Drafts as reference
   material or to cite them other than as "work in progress."

   This Internet-Draft will expire on September 28, 2014.

Copyright Notice

   Copyright (c) 2014 IETF Trust and the persons identified as the
   document authors.  All rights reserved.

   This document is subject to BCP 78 and the IETF Trust's Legal
   Provisions Relating to IETF Documents
   (http://trustee.ietf.org/license-info) in effect on the date of
   publication of this document.  Please review these documents
   carefully, as they describe your rights and restrictions with respect
   to this document.  Code Components extracted from this document must
   include Simplified BSD License text as described in Section 4.e of
   the Trust Legal Provisions and are provided without warranty as
   described in the Simplified BSD License.






D'Amore                Expires September 28, 2014               [Page 1]

Internet-Draft           TLS/TCP mapping for SPs              March 2014


1.  Underlying protocol

   This mapping should be layered directly on the top of TLS [TLS]
   secured connections.  While it is possible to use TLS on top of other
   transports, this document specifically concerns itself with TLS
   running on top of TCP [TCP].

   Other combinations may be contemplated, and should follow the same
   details as discussed here.

   As when running SP over TCP directly, the TCP port number is
   determined by the application or user.

   This mapping follows the details of SP over TCP [SPoverTCP].

2.  Connection initiation

   An initial connection is first established using TCP, then performing
   a TLS handshake.  This handshake establishes the security parameters
   of the connection, including negotiation of cipher suites, exchanging
   keys, and possibly performing one or two-way authentication.

   The specific details of the TLS negotiation are determined by the
   application(s) involved, and are not specified here.  This includes
   selection of the specific version of TLS or possibly falling back to
   SSL version 3 (but not SSL version 1 or 2).

   TLS presents an encrypted channel that may be treated as a full
   duplex byte stream between peers.  This mapping sits within that
   channel.

   Note also that TLS peers may rekey periodically.  This happens in the
   without involving the upper protocol, and the details need not
   concern us here.

   Once the TLS layer connection has been established, the communication
   commences as detailed in SPoverTCP [SPoverTCP].  This includes the
   exchange of the initial protocol headers identifying the version of
   SP in use, and the specific protocol type, as well as requirements to
   disconnect upon receipt of an invalid protocol header or an
   unrecognized SP version.

3.  IANA Considerations

   This memo includes no request to IANA.






D'Amore                Expires September 28, 2014               [Page 2]

Internet-Draft           TLS/TCP mapping for SPs              March 2014


4.  Security Considerations

   Security considerations are explored in depth as part of TLS [TLS].
   This document does not provide any further implications beyond that
   in TLS itself.

   The use of SSLv2 is explicitly forbidden [RFC6176], as SSLv2 contains
   known weaknesses.

5.  References

   [TCP]      Postel, J., "Transmission Control Protocol", STD 7, RFC
              793, September 1981.

   [TLS]      Dierks, T. and E. Rescorla, "The Transport Layer Security
              (TLS) Protocol Version 1.2", RFC 5246, August 2008.

   [RFC6176]  Turner, S. and T. Polk, "Prohibiting Secure Sockets Layer
              (SSL) Version 2.0", RFC 6176, March 2011.

   [SPoverTCP]
              Sustrik, M., "TCP mapping for SPs", August 2013.

Author's Address

   Garrett D'Amore (editor)

   Email: <EMAIL>























D'Amore                Expires September 28, 2014               [Page 3]

