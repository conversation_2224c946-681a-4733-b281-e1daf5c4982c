<?xml version="1.0" encoding="US-ASCII"?>
<!DOCTYPE rfc SYSTEM "rfc2629.dtd">

<rfc category="info" docName="sp-protocol-ids-01">

  <front>

    <title abbrev="List of SP protocol IDs">
    List of SP protocol IDs
    </title>

    <author fullname="<PERSON>" initials="<PERSON><PERSON>" role="editor"
            surname="<PERSON><PERSON><PERSON>">
      <address>
        <email><EMAIL></email>
      </address>
    </author>

    <date month="June" year="2014" />

    <area>Applications</area>
    <workgroup>Internet Engineering Task Force</workgroup>

    <keyword>SP</keyword>
    <keyword>IANA</keyword>

    <abstract>
      <t>This document is intended to be a central repository of SP protocol
          IDs. The intention is to pass the task to IANA later on.</t>
    </abstract>

  </front>

  <middle>

    <section anchor="Introduction" title="Introduction">
      <t>Different mappings for scalability protocols (see, for example
         <xref target='SPoverTCP'>SPoverTCP</xref>) define a protocol header
         which in turn contains SP endpoint type ID. The ID consists of
         protocol ID and end the endpoint role:</t>

      <figure>
        <artwork>
+-----------------------+------------------------+
| Protocol ID (12 bits) | Endpoint role (4 bits) |
+-----------------------+------------------------+
        </artwork>
      </figure>

      <t>Protocol IDs denote the SP protocol used (such as request/reply or
         publish/subscribe), while endpoint role determines the role of the
         endpoint within the topology (requester vs. replier, publisher vs.
         subscriber etc.) Both numbers are in network byte order.</t>

      <t>Protocol IDs are global, while endpoint roles are specific to any given
         protocol. As such, protocol IDs are defined in this document, while
         endpoint roles are defined in specific SP protocol RFCs.</t>

      <t>Note that there's no versioning of SP protocols. New versions of old
         protocols should register with new protocol ID.</t>

    </section>

    <section anchor="ProtocolIDs" title="Protocol IDs">

      <t>1 - pair (v1)</t>
      <t>2 - publish/subscribe (v1)</t>
      <t>3 - request/reply (v1)</t>
      <t>4 - unassigned</t>
      <t>5 - pipeline (v1)</t>
      <t>6 - survey (v1)</t>
      <t>7 - bus (v1)</t>
      <t>8 - slowsand (v1)</t>
      <t>9-3839 - unassigned</t>
      <t>3840-4095 local and experimental</t>

    </section>

    <section anchor="IANA" title="IANA Considerations">
      <t>This memo describes numbers that should be eventually
         managed by IANA.</t>
    </section>

    <section anchor="Security" title="Security Considerations">
      <t>There are no security considerations.</t>

    </section>

  </middle>

  <back>
    <references>
      <reference anchor='SPoverTCP'>
         <front>
           <title>TCP mapping for SPs</title>
           <author initials='M.' surname='Sustrik' fullname='M. Sustrik'/>
           <date month='August' year='2013'/>
         </front>
         <format type='TXT' target='sp-tcp-mapping-01.txt'/>
       </reference>
    </references>

  </back>

</rfc>

