



Internet Engineering Task Force                          G. <PERSON>, Ed.
Internet-Draft
Intended status: Informational                                M. Sustrik
Expires: October 14, 2016
                                                          April 12, 2016


                 IPC Mapping for Scalability Protocols
                           sp-ipc-mapping-01

Abstract

   This document defines the IPC mapping for scalability protocols.  It
   deals with how IPC (inter-process communication) should be
   implemented on POSIX-compliant platforms.

Status of This Memo

   This Internet-Draft is submitted in full conformance with the
   provisions of BCP 78 and BCP 79.

   Internet-Drafts are working documents of the Internet Engineering
   Task Force (IETF).  Note that other groups may also distribute
   working documents as Internet-Drafts.  The list of current Internet-
   Drafts is at http://datatracker.ietf.org/drafts/current/.

   Internet-Drafts are draft documents valid for a maximum of six months
   and may be updated, replaced, or obsoleted by other documents at any
   time.  It is inappropriate to use Internet-Drafts as reference
   material or to cite them other than as "work in progress."

   This Internet-Draft will expire on October 14, 2016.

Copyright Notice

   Copyright (c) 2016 IETF Trust and the persons identified as the
   document authors.  All rights reserved.

   This document is subject to BCP 78 and the IETF Trust's Legal
   Provisions Relating to IETF Documents
   (http://trustee.ietf.org/license-info) in effect on the date of
   publication of this document.  Please review these documents
   carefully, as they describe your rights and restrictions with respect
   to this document.  Code Components extracted from this document must
   include Simplified BSD License text as described in Section 4.e of
   the Trust Legal Provisions and are provided without warranty as
   described in the Simplified BSD License.




D'Amore & Sustrik       Expires October 14, 2016                [Page 1]

Internet-Draft             IPC mapping for SPs                April 2016


1.  Underlying protocol

   This mapping should be layered directly on the top of AF_UNIX sockets
   of type SOCK_STREAM.  On the platforms where AF_UNIX sockets are not
   available IPC mapping may be done in a platform-specific way and
   SHOULD be described in a separate RFC.

   There's no fixed file to use for SP communication.  Instead,
   filenames are assigned to individual services by the user.

2.  Connection initiation

   Before binding the AF_UNIX socket the implementation SHOULD check
   whether there's another process bound to the address.  If not so it
   SHOULD try to delete the associated file, if present.  This measure
   will prevent subsequent bind from failing if there's a leftover file
   from the previous runs of the application.

   The check can be performed in a platform-specific way, however, a
   generic way to implement it is to try to connect to the address and
   close the connection immediately if successful.

   After establishing underlying AF_UNIX connection, both parties MUST
   send the protocol header immediately.  Both endpoints MUST then wait
   for the protocol header from the peer before proceeding on.

   The protocol header is 8 bytes long and looks like this:

    0                   1                   2                   3
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |      0x00     |      0x53     |      0x50     |    version    |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |             type              |           reserved            |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

   First four bytes of the protocol header are used to make sure that
   the peer's protocol is compatible with the protocol used by the local
   endpoint.

   First four bytes of the protocol header MUST be set to 0x00, 0x53,
   0x50 and 0x00 respectively.  If the protocol header received from the
   peer differs, the TCP connection MUST be closed immediately.

   The fact that the first byte of the protocol header is binary zero
   eliminates any text-based protocols that were accidentally connected
   to the endpoint.  Subsequent two bytes make the check even more
   rigorous.  At the same time they can be used as a debugging hint to



D'Amore & Sustrik       Expires October 14, 2016                [Page 2]

Internet-Draft             IPC mapping for SPs                April 2016


   indicate that the connection is supposed to use one of the
   scalability protocols -- ASCII representation of these bytes is 'SP'.
   Finally, the fourth byte rules out any incompatible versions of this
   protocol.

   Fifth and sixth bytes of the header form a 16-bit unsigned integer in
   network byte order representing the type of SP endpoint on the layer
   above.  The value SHOULD NOT be interpreted by the mapping, rather
   the interpretation should be delegated to the scalability protocol
   above the mapping.  For informational purposes, it should be noted
   that the field encodes information such as SP protocol ID, protocol
   version and the role of endpoint within the protocol.  Individual
   values are assigned by IANA.

   Finally, the last two bytes of the protocol header are reserved for
   future use and must be set to binary zeroes.  If the protocol header
   from the peer contains anything else than zeroes in this field, the
   implementation MUST close the underlying TCP connection.

3.  Message header

   Once the protocol header is accepted, endpoint can send and receive
   messages.  Every message starts with a message header consisting of
   of a single byte called 'message type'.

   The only value of this field that is currently allowed is 0x1, which
   means "in-band" message, i.e. message whose body is passed as a
   stream of bytes via the AF_UNIX socket.

   The intent of this field is to eventually allow out-of-band transfer
   of the message bodies, e.g. via shared memory.

   The in-band message type MUST be implemented.

4.  In-band messages

   For in-band messages, message header is immediately followed by
   64-bit unsigned integer in network byte order representing the
   payload size, in bytes.  Thus, the message payload can be from 0 to
   2^64-1 bytes long.  The payload of the specified size follows
   directly after the size field:

   +-----------+------------+-----------------+
   | type (8b) | size (64b) |     payload     |
   +-----------+------------+-----------------+






D'Amore & Sustrik       Expires October 14, 2016                [Page 3]

Internet-Draft             IPC mapping for SPs                April 2016


5.  IANA Considerations

   This memo includes no request to IANA.

6.  Security Considerations

   The mapping isn't intended to provide any additional security in
   addition to what AF_UNIX does.

Authors' Addresses

   Garrett D'Amore (editor)

   Email: <EMAIL>


   Martin Sustrik

   Email: <EMAIL>
































D'Amore & Sustrik       Expires October 14, 2016                [Page 4]
