nn_survey(7)
============

NAME
----
nn_survey - survey scalability protocol


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*#include <nanomsg/survey.h>*


DESCRIPTION
-----------
Allows one to broadcast a survey to multiple locations and gather the responses.

Socket Types
~~~~~~~~~~~~

NN_SURVEYOR::
    Used to send the survey. The survey is delivered to all the connected
    respondents. Once the query is sent, the socket can be used to receive
    the responses. When the survey deadline expires, receive will return
    ETIMEDOUT error.
NN_RESPONDENT::
    Use to respond to the survey. Survey is received using receive function,
    response is sent using send function. This socket can be connected to
    at most one peer.


Socket Options
~~~~~~~~~~~~~~

NN_SURVEYOR_DEADLINE::
    Specifies how long to wait for responses to the survey. Once the deadline
    expires, receive function will return ETIMEDOUT error and all subsequent
    responses to the survey will be silently dropped. The deadline is measured
    in milliseconds. Option type is int. Default value is 1000 (1 second).


SEE ALSO
--------
<<nn_bus#,nn_bus(7)>>
<<nn_pubsub#,nn_pubsub(7)>>
<<nn_reqrep#,nn_reqrep(7)>>
<<nn_pipeline#,nn_pipeline(7)>>
<<nn_pair#,nn_pair(7)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

