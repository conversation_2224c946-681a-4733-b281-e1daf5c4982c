nn_sendmsg(3)
=============

NAME
----
nn_sendmsg - fine-grained alternative to nn_send


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_sendmsg (int 's', const struct nn_msghdr '*msghdr', int 'flags');*

DESCRIPTION
-----------

Sends data specified by 'msghdr' parameter to socket 's' along with any
additional control data. 'msghdr' structure should be nullified before being
used.

Structure 'nn_msghdr' contains at least following members:

    struct nn_iovec *msg_iov;
    int msg_iovlen;
    void *msg_control;
    size_t msg_controllen;

'msg_iov' points to a scatter array of buffers to send. 'msg_iovlen' specifies
the size of the array.

'msg_control' points to the buffer containing control information to be
associated with the message being sent. 'msg_controllen' specifies the length
of the buffer. If there's no control information to send, 'msg_control' should
be set to NULL. For detailed discussion of how to set control data check
<<nn_cmsg#,nn_cmsg(3)>> man page.

Structure 'nn_iovec' defines one element in the scatter array (i.e. a buffer
to send to the socket) and contains following members:

    void *iov_base;
    size_t iov_len;

Alternatively, to send a buffer allocated by <<nn_allocmsg#,nn_allocmsg(3)>> function
set 'iov_base' to point to the pointer to the buffer and 'iov_len' to _NN_MSG_
constant. In this case a successful call to _nn_sendmsg_ will deallocate the
buffer. Trying to deallocate it afterwards will result in undefined behaviour.
Also, scatter array in _nn_msghdr_ structure can contain only one element
in this case.

To which of the peers will the message be sent to is determined by
the particular socket type.

The 'flags' argument is a combination of the flags defined below:

*NN_DONTWAIT*::
Specifies that the operation should be performed in non-blocking mode. If the
message cannot be sent straight away, the function will fail with 'errno' set
to EAGAIN.


RETURN VALUE
------------
If the function succeeds number of bytes in the message is returned. Otherwise,
-1 is returned and 'errno' is set to to one of the values defined below.


ERRORS
------
*EINVAL*::
Either 'msghdr' is NULL, there are multiple scatter buffers but length is
set to 'NN_MSG' for one of them, or the sum of 'iov_len' values for the
scatter buffers overflows 'size_t'. These are early checks and no
pre-allocated message is freed in this case.
*EMSGSIZE*::
msghdr->msg_iovlen is negative. This is an early check and no pre-allocated
message is freed in this case.
*EFAULT*::
The supplied pointer for the pre-allocated message buffer or the scatter
buffer is NULL, or the length for the scatter buffer is 0.
*EBADF*::
The provided socket is invalid.
*ENOTSUP*::
The operation is not supported by this socket type.
*EFSM*::
The operation cannot be performed on this socket at the moment because socket is
not in the appropriate state.  This error may occur with socket types that
switch between several states.
*EAGAIN*::
Non-blocking mode was requested and the message cannot be sent at the moment.
*EINTR*::
The operation was interrupted by delivery of a signal before the message was
sent.
*ETIMEDOUT*::
Individual socket types may define their own specific timeouts. If such timeout
is hit this error will be returned.
*ETERM*::
The library is terminating.


EXAMPLE
-------

Usage of multiple scatter buffers:

----
struct nn_msghdr hdr;
struct nn_iovec iov [2];

iov [0].iov_base = "Hello";
iov [0].iov_len = 5;
iov [1].iov_base = "World";
iov [1].iov_len = 5;
memset (&hdr, 0, sizeof (hdr));
hdr.msg_iov = iov;
hdr.msg_iovlen = 2;
nn_sendmsg (s, &hdr, 0);
----

Usage of a single message:

----
void *msg;
struct nn_msghdr hdr;
struct nn_iovec iov;

msg = nn_allocmsg(12, 0);
strcpy(msg, "Hello World");
iov.iov_base = &msg;
iov.iov_len = NN_MSG;
memset (&hdr, 0, sizeof (hdr));
hdr.msg_iov = &iov;
hdr.msg_iovlen = 1;
nn_sendmsg (s, &hdr, 0);
----


SEE ALSO
--------
<<nn_send#,nn_send(3)>>
<<nn_recvmsg#,nn_recvmsg(3)>>
<<nn_allocmsg#,nn_allocmsg(3)>>
<<nn_freemsg#,nn_freemsg(3)>>
<<nn_cmsg#,nn_cmsg(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

