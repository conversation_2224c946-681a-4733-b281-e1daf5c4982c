nn_freemsg(3)
=============

NAME
----
nn_freemsg - deallocate a message


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_freemsg (void '*msg');*


DESCRIPTION
-----------
Deallocates a message allocated using <<nn_allocmsg#,nn_allocmsg(3)>> function or
received via <<nn_recv#,nn_recv(3)>> or <<nn_recvmsg#,nn_recvmsg(3)>> function.
While <<nn_recv#,nn_recv(3)>> and <<nn_recvmsg#,nn_recvmsg(3)>> allow one to receive data
into arbitrary buffers, using library-allocated buffers can be more
efficient for large messages as it allows for using zero-copy techniques.


RETURN VALUE
------------
If the function succeeds zero is returned. Otherwise, -1 is
returned and 'errno' is set to to one of the values defined below.


ERRORS
------
*EFAULT*::
The message pointer is invalid.


EXAMPLE
-------

----
void *buf;
nn_recv (s, &buf, NN_MSG, 0);
nn_freemsg (buf);
----


SEE ALSO
--------
<<nn_allocmsg#,nn_allocmsg(3)>>
<<nn_reallocmsg#,nn_reallocmsg(3)>>
<<nn_recv#,nn_recv(3)>>
<<nn_recvmsg#,nn_recvmsg(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

