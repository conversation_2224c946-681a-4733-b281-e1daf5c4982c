nn_errno(3)
===========

NAME
----
nn_errno - retrieve the current errno


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_errno (void);*


DESCRIPTION
-----------
Returns value of 'errno' for the current thread.

On most systems, 'errno' can be accessed directly and this function is not
needed. However, on Windows, there are multiple implementations of the CRT
library (single-threaded, multi-threaded, release, debug) and each of them
has its own instance of 'errno'. Thus, if nanomsg library and the application
that uses it link with different versions of the CRT library, they don't share
the same instance of 'errno'. Consequently, error codes set by nanomsg cannot be
accessed by the application. To overcome this problem, application can use
_nn_errno()_ function to retrieve nanomsg's value of 'errno'.

RETURN VALUE
------------
Returns value of 'errno' for the current thread.


ERRORS
------
No errors are defined.


EXAMPLE
-------

----
rc = nn_send (s, "ABC", 3, 0);
if (rc < 0)
    printf ("nn_send failed with error code %d\n", nn_errno ());
----


SEE ALSO
--------
<<nn_strerror#,nn_strerror(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

