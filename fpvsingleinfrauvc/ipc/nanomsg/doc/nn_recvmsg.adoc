nn_recvmsg(3)
=============

NAME
----
nn_recvmsg - fine-grained alternative to nn_recv


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*NN_EXPORT int nn_recvmsg (int 's', struct nn_msghdr '*msghdr', int 'flags');*


DESCRIPTION
-----------
Receives a message from socket 's' into buffers specified by 'msghdr' parameter
along with any additional control data. 'msghdr' parameter should be nullified
before being used.

Structure 'nn_msghdr' contains at least following members:

    struct nn_iovec *msg_iov;
    int msg_iovlen;
    void *msg_control;
    size_t msg_controllen;

'msg_iov' points to a gather array of buffers to fill in. 'msg_iovlen' specifies
the size of the array.

'msg_control' points to the buffer to hold control information  associated with
the received message. 'msg_controllen' specifies the length of the buffer.
If the control information should not be retrieved, set 'msg_control' parameter
to NULL. For detailed discussion of how to parse the control information check
<<nn_cmsg#,nn_cmsg(3)>> man page.

Structure 'nn_iovec' defines one element in the gather array (a buffer to be
filled in by message data) and contains following members:

    void *iov_base;
    size_t iov_len;

Alternatively, _nanomsg_ library can allocate the buffer for you. To do so,
let the 'iov_base' point to void* variable to receive the buffer and set
'iov_len' to _NN_MSG_. After successful completion user is responsible
for deallocating the message using <<nn_freemsg#,nn_freemsg(3)>> function. Gather
array in _nn_msghdr_ structure can contain only one element in this case.

The 'flags' argument is a combination of the flags defined below:

*NN_DONTWAIT*::
Specifies that the operation should be performed in non-blocking mode. If the
message cannot be received straight away, the function will fail with 'errno'
set to EAGAIN.


RETURN VALUE
------------
If the function succeeds number of bytes in the message is returned. Otherwise,
-1 is returned and 'errno' is set to to one of the values defined
below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*ENOTSUP*::
The operation is not supported by this socket type.
*EFSM*::
The operation cannot be performed on this socket at the moment because socket is
not in the appropriate state.  This error may occur with socket types that
switch between several states.
*EAGAIN*::
Non-blocking mode was requested and there's no message to receive at the moment.
*EINTR*::
The operation was interrupted by delivery of a signal before the message was
received.
*ETIMEDOUT*::
Individual socket types may define their own specific timeouts. If such timeout
is hit this error will be returned.
*ETERM*::
The library is terminating.

EXAMPLE
-------

----
struct nn_msghdr hdr;
struct nn_iovec iov [2];
char buf0 [4];
char buf1 [2];
iov [0].iov_base = buf0;
iov [0].iov_len = sizeof (buf0);
iov [1].iov_base = buf1;
iov [1].iov_len = sizeof (buf1);
memset (&hdr, 0, sizeof (hdr));
hdr.msg_iov = iov;
hdr.msg_iovlen = 2;
nn_recvmsg (s, &hdr, 0);
----


SEE ALSO
--------
<<nn_recv#,nn_recv(3)>>
<<nn_sendmsg#,nn_sendmsg(3)>>
<<nn_allocmsg#,nn_allocmsg(3)>>
<<nn_freemsg#,nn_freemsg(3)>>
<<nn_cmsg#,nn_cmsg(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

