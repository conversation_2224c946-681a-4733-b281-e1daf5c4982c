nn_allocmsg(3)
==============

NAME
----
nn_allocmsg - allocate a message


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*void *nn_allocmsg (size_t 'size', int 'type');*


DESCRIPTION
-----------
Allocate a message of the specified 'size' to be sent in zero-copy fashion.
The content of the message is undefined after allocation and it should be filled
in by the user. While <<nn_send#,nn_send(3)>> and <<nn_sendmsg#,nn_sendmsg(3)>> allow
to send arbitrary buffers, buffers allocated using _nn_allocmsg()_ can be more
efficient for large messages as they allow for using zero-copy techniques.

'type' parameter specifies type of allocation mechanism to use. Zero is the
default one, however, individual transport mechanisms may define their
own allocation mechanisms, such as allocating in shared memory or allocating
a memory block pinned down to a physical memory address. Such allocation,
when used with the transport that defines them, should be more efficient
than the default allocation mechanism.


RETURN VALUE
------------
If the function succeeds pointer to newly allocated buffer is returned.
Otherwise, NULL is returned and 'errno' is set to to one of the values
defined below.


ERRORS
------
*EINVAL*::
Supplied allocation 'type' is invalid.
*ENOMEM*::
Not enough memory to allocate the message.


EXAMPLE
-------

----
void *buf = nn_allocmsg (12, 0);
memcpy (buf, "Hello world!", 12);
nn_send (s, &buf, NN_MSG, 0);
----


SEE ALSO
--------
<<nn_freemsg#,nn_freemsg(3)>>
<<nn_reallocmsg#,nn_reallocmsg(3)>>
<<nn_send#,nn_send(3)>>
<<nn_sendmsg#,nn_sendmsg(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

