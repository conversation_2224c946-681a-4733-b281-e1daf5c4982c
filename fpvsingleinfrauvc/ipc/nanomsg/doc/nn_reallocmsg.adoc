nn_reallocmsg(3)
================

NAME
----
nn_reallocmsg - reallocate a message


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*void *nn_reallocmsg (void *'msg', size_t 'size');*

DESCRIPTION
-----------
Reallocate a message previously allocated by <<nn_allocmsg#,nn_allocmsg(3)>> or
received from a peer using NN_MSG mechanism.

Note that as with the standard _realloc_, the operation may involve copying
the data in the buffer.


RETURN VALUE
------------
If the function succeeds pointer to newly allocated buffer is returned.
Otherwise, NULL is returned and 'errno' is set to to one of the values
defined below.


ERRORS
------
*ENOMEM*::
Not enough memory to allocate the message.


EXAMPLE
-------

----
void *buf = nn_allocmsg (12, 0);
void *newbuf = nn_reallocmsg (buf, 20);
nn_freemsg (newbuf);
----


SEE ALSO
--------
<<nn_allocmsg#,nn_allocmsg(3)>>
<<nn_freemsg#,nn_freemsg(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

