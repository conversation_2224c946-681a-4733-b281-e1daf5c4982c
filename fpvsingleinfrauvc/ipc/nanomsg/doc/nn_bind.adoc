nn_bind(3)
==========

NAME
----
nn_bind - add a local endpoint to the socket


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_bind (int 's', const char '*addr');*


DESCRIPTION
-----------
Adds a local endpoint to the socket 's'. The endpoint can be then used by other
applications to connect to.

The 'addr' argument consists of two parts as follows: 'transport'`://`'address'.
The 'transport' specifies the underlying transport protocol to use. The meaning
of the 'address' part is specific to the underlying transport protocol.

For the list of available transport protocols check the list on
<<nanomsg#,nanomsg(7)>> manual page.

Maximum length of the 'addr' parameter is specified by _NN_SOCKADDR_MAX_
defined in '<nanomsg/nn.h>' header file.

Note that nn_bind and <<nn_connect#,nn_connect(3)>> may be called multiple times
on the same socket thus allowing the socket to communicate with multiple
heterogeneous endpoints.

NOTE
----

Unlike with traditional BSD sockets, this function operates asynchronously,
and returns to the caller before the operation is complete.
As a result, attempts to send data or receive data on the socket may not
succeed until the underlying transport actually establishes a connection.

RETURN VALUE
------------
If the function succeeds positive endpoint ID is returned. Endpoint ID can be
later used to remove the endpoint from the socket via <<nn_shutdown#,nn_shutdown(3)>>
function.

If the function fails, then -1 is returned and 'errno' is set to to one of
the values defined below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*EMFILE*::
Maximum number of active endpoints was reached.
*EINVAL*::
The syntax of the supplied address is invalid.
*ENAMETOOLONG*::
The supplied address is too long.
*EPROTONOSUPPORT*::
The requested transport protocol is not supported.
*EADDRNOTAVAIL*::
The requested endpoint is not local.
*ENODEV*::
Address specifies a nonexistent interface.
*EADDRINUSE*::
The requested local endpoint is already in use.
*ETERM*::
The library is terminating.


EXAMPLE
-------

----
s = nn_socket (AF_SP, NN_PUB);
eid1 = nn_bind (s, "inproc://test");
eid2 = nn_bind (s, "tcp://127.0.0.1:5560");
----


SEE ALSO
--------
<<nn_inproc#,nn_inproc(7)>>
<<nn_ipc#,nn_ipc(7)>>
<<nn_tcp#,nn_tcp(7)>>
<<nn_socket#,nn_socket(3)>>
<<nn_connect#,nn_connect(3)>>
<<nn_shutdown#,nn_shutdown(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]
link:mailto:<EMAIL>[Garrett D'Amore]

