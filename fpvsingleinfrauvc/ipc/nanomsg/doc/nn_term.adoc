nn_term(3)
==========

NAME
----
nn_term - notify all sockets about process termination


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*void nn_term (void);*


DESCRIPTION
-----------
To help with shutdown of multi-threaded programs nanomsg provides the
_nn_term()_ function which closes all open sockets, and releases all
related resources.

If a socket is blocked inside a blocking function, such as
<<nn_recv#,nn_recv(3)>>, it will be unblocked  and EBADF error will be returned
to the user.  Subsequent calls on such sockets will also return EBADF.

Attempting to open a new socket with <<nn_socket#,nn_socket(3)>> will
result in an ETERM error.

If waiting for _NN_SNDFD_ or _NN_RCVFD_ using a polling function, such as
_poll()_ or _select()_, the call will unblock with both _NN_SNDFD_ and
_NN_RCVFD_ signaled.


EXAMPLE
-------

----
s = nn_socket (AF_SP, NN_PAIR);
nn_term ();
rc = nn_send (s, "ABC", 3, 0);
assert (rc == -1 && errno == EBADF);
----


SEE ALSO
--------
<<nn_close#,nn_close(3)>>
<<nn_send#,nn_send(3)>>
<<nn_socket#,nn_socket(3)>>
<<nn_recv#,nn_recv(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]
link:mailto:<EMAIL>[Garrett D'Amore]

