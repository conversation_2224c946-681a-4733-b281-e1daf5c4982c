nanomsg(7)
==========


NAME
----
nanomsg - scalability protocols library


SYNOPSIS
--------
*cc* ['flags'] 'files' *-lnanomsg* ['libraries']


DESCRIPTION
-----------

Following functions are exported by nanomsg library:

Create an SP socket::
    <<nn_socket#,nn_socket(3)>>

Close an SP socket::
    <<nn_close#,nn_close(3)>>

Set a socket option::
    <<nn_setsockopt#,nn_setsockopt(3)>>

Retrieve a socket option::
    <<nn_getsockopt#,nn_getsockopt(3)>>

Add a local endpoint to the socket::
    <<nn_bind#,nn_bind(3)>>

Add a remote endpoint to the socket::
    <<nn_connect#,nn_connect(3)>>

Remove an endpoint from the socket::
    <<nn_shutdown#,nn_shutdown(3)>>

Send a message::
    <<nn_send#,nn_send(3)>>

Receive a message::
    <<nn_recv#,nn_recv(3)>>

Fine-grained alternative to nn_send::
    <<nn_sendmsg#,nn_sendmsg(3)>>

Fine-grained alternative to nn_recv::
    <<nn_recvmsg#,nn_recvmsg(3)>>

Allocation of messages::
    <<nn_allocmsg#,nn_allocmsg(3)>>
    <<nn_reallocmsg#,nn_reallocmsg(3)>>
    <<nn_freemsg#,nn_freemsg(3)>>

Manipulation of message control data::
    <<nn_cmsg#,nn_cmsg(3)>>

Multiplexing::
    <<nn_poll#,nn_poll(3)>>

Retrieve the current errno::
    <<nn_errno#,nn_errno(3)>>

Convert an error number into human-readable string::
    <<nn_strerror#,nn_strerror(3)>>

Query the names and values of nanomsg symbols::
    <<nn_symbol#,nn_symbol(3)>>

Query properties of nanomsg symbols::
    <<nn_symbol_info#,nn_symbol_info(3)>>

Query statistics on a socket::
    <<nn_get_statistic#,nn_get_statistic(3)>>

Start a device::
    <<nn_device#,nn_device(3)>>

Notify all sockets about process termination::
    <<nn_term#,nn_term(3)>>

Environment variables that influence nanomsg work::
    <<nn_env#,nn_env(7)>>

Following scalability protocols are provided by nanomsg:

One-to-one protocol::
    <<nn_pair#,nn_pair(7)>>

Request/reply protocol::
    <<nn_reqrep#,nn_reqrep(7)>>

Publish/subscribe protocol::
    <<nn_pubsub#,nn_pubsub(7)>>

Survey protocol::
    <<nn_survey#,nn_survey(7)>>

Pipeline protocol::
    <<nn_pipeline#,nn_pipeline(7)>>

Message bus protocol::
    <<nn_bus#,nn_bus(7)>>

Following transport mechanisms are provided by nanomsg:

In-process transport::
    <<nn_inproc#,nn_inproc(7)>>

Inter-process transport::
    <<nn_ipc#,nn_ipc(7)>>

TCP transport::
    <<nn_tcp#,nn_tcp(7)>>

WebSocket transport::
    <<nn_ws#,nn_ws(7)>>

The following tool is installed with the library:

nanocat::
    <<nanocat#,nanocat(1)>>

AUTHORS
-------
link:mailto:<EMAIL>[Garrett D'Amore]
link:mailto:<EMAIL>[Martin Sustrik]

