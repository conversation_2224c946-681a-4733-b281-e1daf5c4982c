nn_env(7)
==========


NAME
----
nn_env - nanomsg environment variables


SYNOPSIS
--------
Environment variables that influence the way nanomsg works


DESCRIPTION
-----------

*This functionality is experimental and a subject to change at any time*

Following environment variables are used to turn on some debugging for any
nanomsg application. Please, do not try to parse output and do not
build business logic based on it.

NN_PRINT_ERRORS::
    If set to a non-empty string nanomsg will print errors to stderr. Some
    errors will be resolved by nanomsg itself (e.g. if nanomsg can't establish
    connection it will retry again in a moment). Some depend on the
    environment (e.g. port that is bound by another process need to be
    released). In any case nanomsg will not repeat the error message until
    error is clear and appear again (e.g. connection established then broken
    again).


NOTES
-----

The output of the debugging facility (NN_PRINT_ERRORS)
is intended for reading by a human and a subject for change at any time (even
after 1.0 release).


SEE ALSO
--------
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[<PERSON>]

