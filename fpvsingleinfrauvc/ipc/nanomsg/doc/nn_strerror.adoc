nn_strerror(3)
==============

NAME
----
nn_strerror - convert an error number into human-readable string


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*const char *nn_strerror (int 'errnum');*


DESCRIPTION
-----------
Converts error number (errno) into a human-readable string. As opposed to
'strerror(3)' this function handles nanomsg-specific errors in addition
to standard system errors.


RETURN VALUE
------------
Return error message string.


ERRORS
------
No errors are defined.


EXAMPLE
-------

----
rc = nn_send (s, "ABC", 3, 0);
if (rc < 0)
    printf ("nn_send failed: %s\n", nn_strerror (errno));
----

SEE ALSO
--------
<<nn_errno#,nn_errno(3)>>
<<nn_symbol#,nn_symbol(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[<PERSON>]

