nn_device(3)
============

NAME
----
nn_device - start a device


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_device (int 's1', int 's2');*


DESCRIPTION
-----------
Starts a device to forward messages between two sockets. If both sockets are
valid, _nn_device_ function loops and sends any messages received from 's1' to
's2' and vice versa. If only one socket is valid and the other is negative,
_nn_device_ works in a "loopback" mode -- it loops and sends any messages
received from the socket back to itself.

To break the loop and make _nn_device_ function exit use the
<<nn_term#,nn_term(3)>> function.

RETURN VALUE
------------
The function loops until it hits an error. In such a case it returns -1
and sets 'errno' to one of the values defined below.

ERRORS
------
*EBADF*::
One of the provided sockets is invalid.
*EINVAL*::
Either one of the socket is not an AF_SP_RAW socket; or the two sockets don't
belong to the same protocol; or the directionality of the sockets doesn't fit
(e.g. attempt to join two SINK sockets to form a device).
*EINTR*::
The operation was interrupted by delivery of a signal.
*ETERM*::
The library is terminating.

EXAMPLE
-------

----
int s1 = nn_socket (AF_SP_RAW, NN_REQ);
nn_bind (s1, "tcp://127.0.0.1:5555");
int s2 = nn_socket (AF_SP_RAW, NN_REP);
nn_bind (s2, "tcp://127.0.0.1:5556");
nn_device (s1, s2);
----


SEE ALSO
--------
<<nn_socket#,nn_socket(3)>>
<<nn_term#,nn_term(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

