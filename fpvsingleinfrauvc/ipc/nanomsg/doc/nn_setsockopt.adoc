nn_setsockopt(3)
================

NAME
----
nn_setsockopt - set a socket option


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_setsockopt (int 's', int 'level', int 'option', const void '*optval', size_t 'optvallen');*


DESCRIPTION
-----------
Sets the value of the 'option'. The 'level' argument specifies the protocol
level at which the option resides. For generic socket-level options use
_NN_SOL_SOCKET_ level. For socket-type-specific options use socket type
for 'level' argument (e.g. _NN_SUB_). For transport-specific options use ID of
the transport as the 'level' argument (e.g. _NN_TCP_).

The new value is pointed to by 'optval' argument. Size of the option is
specified by the 'optvallen' argument.

_<nanomsg/nn.h>_ header defines generic socket-level options
(_NN_SOL_SOCKET_ level). The options are as follows:

*NN_SNDBUF*::
    Size of the send buffer, in bytes. To prevent blocking for messages larger
    than the buffer, exactly one message may be buffered in addition to the data
    in the send buffer. The type of this option is int. Default value is 128kB.
*NN_RCVBUF*::
    Size of the receive buffer, in bytes. To prevent blocking for messages
    larger than the buffer, exactly one message may be buffered in addition
    to the data in the receive buffer. The type of this option is int. Default
    value is 128kB.
*NN_RCVMAXSIZE*::
    Maximum message size that can be received, in bytes. Negative value means
    that the received size is limited only by available addressable memory. The
    type of this option is int. Default is 1024kB.
*NN_SNDTIMEO*::
    The timeout for send operation on the socket, in milliseconds. If message
    cannot be sent within the specified timeout, ETIMEDOUT error is returned.
    Negative value means infinite timeout. The type of the option is int.
    Default value is -1.
*NN_RCVTIMEO*::
    The timeout for recv operation on the socket, in milliseconds. If message
    cannot be received within the specified timeout, ETIMEDOUT error is
    returned.  Negative value means infinite timeout. The type of the option
    is int. Default value is -1.
*NN_RECONNECT_IVL*::
    For connection-based transports such as TCP, this option specifies how
    long to wait, in milliseconds, when connection is broken before trying
    to re-establish it. Note that actual reconnect interval may be randomised
    to some extent to prevent severe reconnection storms. The type of the option
    is int. Default value is 100 (0.1 second).
*NN_RECONNECT_IVL_MAX*::
    This option is to be used only in addition to _NN_RECONNECT_IVL_ option.
    It specifies maximum reconnection interval. On each reconnect attempt,
    the previous interval is doubled until _NN_RECONNECT_IVL_MAX_ is reached.
    Value of zero means that no exponential backoff is performed and reconnect
    interval is based only on _NN_RECONNECT_IVL_. If _NN_RECONNECT_IVL_MAX_ is
    less than _NN_RECONNECT_IVL_, it is ignored. The type of the option is int.
    Default value is 0.
*NN_SNDPRIO*::
    Sets outbound priority for endpoints subsequently added to the socket. This
    option has no effect on socket types that send messages to all the peers.
    However, if the socket type sends each message to a single peer
    (or a limited set of peers), peers with high priority take precedence
    over peers with low priority. The type of the option is int. Highest
    priority is 1, lowest priority is 16. Default value is 8.
*NN_RCVPRIO*::
    Sets inbound priority for endpoints subsequently added to the socket. This
    option has no effect on socket types that are not able to receive messages.
    When receiving a message, messages from peer with higher priority are
    received before messages from peer with lower priority. The type of the
    option is int. Highest priority is 1, lowest priority is 16. Default value
    is 8.
*NN_IPV4ONLY*::
    If set to 1, only IPv4 addresses are used. If set to 0, both IPv4 and IPv6
    addresses are used. The type of the option is int. Default value is 1.
*NN_SOCKET_NAME*::
    Socket name for error reporting and statistics. The type of the option
    is string. Default value is "socket.N" where N is socket integer.
    *This option is experimental, see <<nn_env#,nn_env(7)>> for details*
*NN_MAXTTL*::
    Sets the maximum number of "hops" a message can go through before
    it is dropped.  Each time the message is received (for example via
    the <<nn_device#,nn_device(3)>> function) counts as a single hop.
    This provides a form of protection against inadvertent loops.
*NN_LINGER*::
    This option is not implemented, and should not be used in new code.
    Applications which need to be sure that their messages are delivered
    to a remote peer should either use an acknowledgement (implied when
    receiving a reply on <<nn_reqrep#,NN_REQ>> sockets), or insert
    a suitable delay before calling <<nn_close#,nn_close(3)>> or
    exiting the application.



RETURN VALUE
------------
If the function succeeds zero is returned. Otherwise, -1 is
returned and 'errno' is set to to one of the values defined below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*ENOPROTOOPT*::
The option is unknown at the level indicated.
*EINVAL*::
The specified option value is invalid.
*ETERM*::
The library is terminating.

EXAMPLE
-------

----
int linger = 1000;
nn_setsockopt (s, NN_SOL_SOCKET, NN_LINGER, &linger, sizeof (linger));
nn_setsockopt (s, NN_SUB, NN_SUB_SUBSCRIBE, "ABC", 3);
----


SEE ALSO
--------
<<nn_socket#,nn_socket(3)>>
<<nn_getsockopt#,nn_getsockopt(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]
link:mailto:<EMAIL>[Garrett D'Amore]

