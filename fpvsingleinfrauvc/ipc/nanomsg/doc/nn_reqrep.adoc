nn_reqrep(7)
============

NAME
----
nn_reqrep - request/reply scalability protocol


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*#include <nanomsg/reqrep.h>*


DESCRIPTION
-----------
This protocol is used to distribute the workload among multiple stateless
workers.

Please note that request/reply applications should be stateless.

It's important to include all the information necessary to process the
request in the request itself, including information about the sender or 
the originator of the request if this is necessary to respond to the request.

Sender information cannot be retrieved from the underlying socket connection
since, firstly, transports like IPC may not have a firm notion of a message
origin. Secondly, transports that have some notion may not have a reliable one
-- a TCP disconnect may mean a new sender, or it may mean a temporary loss in
network connectivity.

For this reason, sender information must be included by the application if
required. Allocating 6 randomly-generated bytes in the message for the lifetime
of the connection is sufficient for most purposes. For longer-lived
applications, an UUID is more suitable.


Socket Types
~~~~~~~~~~~~

NN_REQ::
    Used to implement the client application that sends requests and
    receives replies.
NN_REP::
    Used to implement the stateless worker that receives requests and sends
    replies.

Socket Options
~~~~~~~~~~~~~~

NN_REQ_RESEND_IVL::
    This option is defined on the full REQ socket. If reply is not received
    in specified amount of milliseconds, the request will be automatically
    resent. The type of this option is int. Default value is 60000 (1 minute).

SEE ALSO
--------
<<nn_bus#,nn_bus(7)>>
<<nn_pubsub#,nn_pubsub(7)>>
<<nn_pipeline#,nn_pipeline(7)>>
<<nn_survey#,nn_survey(7)>>
<<nn_pair#,nn_pair(7)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

