nn_shutdown(3)
==============

NAME
----
nn_shutdown - remove an endpoint from a socket


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_shutdown (int 's', int 'how');*


DESCRIPTION
-----------
Removes an endpoint from socket 's'. 'how' parameter specifies the ID of the
endpoint to remove as returned by prior call to <<nn_bind#,nn_bind(3)>> or
<<nn_connect#,nn_connect(3)>>. _nn_shutdown()_ call will return immediately,
however, the library will try to deliver any outstanding outbound messages to
the endpoint for the time specified by _NN_LINGER_ socket option.


RETURN VALUE
------------
If the function succeeds zero is returned. Otherwise, -1 is
returned and 'errno' is set to to one of the values defined below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*EINVAL*::
The 'how' parameter doesn't correspond to an active endpoint.
*EINTR*::
Operation was interrupted by a signal. The endpoint is not fully closed yet.
Operation can be re-started by calling _nn_shutdown()_ again.
*ETERM*::
The library is terminating.


EXAMPLE
-------

----
s = nn_socket (AF_SP, NN_PUB);
eid = nn_bind (s, "inproc://test");
nn_shutdown (s, eid);
----


SEE ALSO
--------
<<nn_socket#,nn_socket(3)>>
<<nn_bind#,nn_bind(3)>>
<<nn_connect#,nn_connect(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

