nn_cmsg(3)
==========

NAME
----
nn_cmsg - access control information


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*struct nn_cmsghdr *NN_CMSG_FIRSTHDR(struct nn_msghdr '*hdr');*

*struct nn_cmsghdr *NN_CMSG_NXTHDR(struct nn_msghdr '*hdr', struct nn_cmsghdr '*cmsg');*

*unsigned char *NN_CMSG_DATA(struct nn_cmsghdr '*cmsg');*

*size_t NN_CMSG_SPACE(size_t 'len');*

*size_t NN_CMSG_LEN(size_t 'len');*

DESCRIPTION
-----------

These functions can be used to iterate over ancillary data attached to a message.

Structure 'nn_cmsghdr' represents a single ancillary property and contains following members:

    size_t cmsg_len;
    int cmsg_level;
    int cmsg_type;

'cmsg_len' is the size of the property data, including the preceding nn_cmsghdr structure.
'cmsg_level' is the level of the property; same values can be used as with <<nn_getsockopt#,nn_getsockopt(3)>> and <<nn_setsockopt#,nn_setsockopt(3)>>.
'cmsg_type' is the name of the property. These names are specific for each level.

_NN_CMSG_FIRSTHDR_ returns a pointer to the first nn_cmsghdr in the control buffer in the supplied nn_msghdr structure.

_NN_CMSG_NXTHDR_ returns the next nn_cmsghdr after the supplied nn_cmsghdr. Returns NULL if there isn't enough space in the buffer.

_NN_CMSG_DATA_ returns a pointer to the data associated with supplied nn_cmsghdr.

_NN_CMSG_SPACE_ returns the number of bytes occupied by nn_cmsghdr with payload of the specified length.

_NN_CMSG_LEN_ returns the value to store in the cmsg_len member of the cmsghdr structure, taking into account any  necessary  alignment.

EXAMPLE
-------

Iterating over ancillary properties:

----
struct nn_cmsghdr *hdr = NN_CMSG_FIRSTHDR (&msg);
while (hdr != NULL) {
    size_t len = hdr->cmsg_len - sizeof (nn_cmsghdr);
    printf ("level: %d property: %d length: %dB data: ",
        (int) hdr->cmsg_level,
        (int) hdr->cmsg_type,
        (int) len);
    unsigned char *data = NN_CMSG_DATA(hdr);
    while (len) {
        printf ("%02X", *data);
        ++data;
        --len;
    }
    printf ("\n");
    hdr = NN_CMSG_NXTHDR (&msg, hdr);
}
----

SEE ALSO
--------
<<nn_sendmsg#,nn_sendmsg(3)>>
<<nn_recvmsg#,nn_recvmsg(3)>>
<<nn_getsockopt#,nn_getsockopt(3)>>
<<nn_setsockopt#,nn_setsockopt(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

