nn_get_statistic(3)
===================

NAME
----
nn_get_statistic - retrieve statistics from nanomsg socket


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*uint64_t nn_get_statistic (int 's', int 'statistic');*


DESCRIPTION
-----------
Retrieves the value of a statistic from the socket.

CAUTION: While this API is stable, these statistics are intended for
human consumption, to facilitate observability and debugging.
The actual statistics themselves as well as their meanings are unstable,
and subject to change without notice.
Programs should not depend on the presence or values of any particular
statistic.

The following statistics are maintained by the nanomsg core framework;
others may be present. As those are undocumented, no interpretration should
be made from them. Not all statistics are relevant to all transports.
For example, the <<nn_inproc#,nn_inproc(7)>> transport does not
maintain any of the connection related statistics.


*NN_STAT_ESTABLISHED_CONNECTIONS*::
    The number of connections successfully established that were initiated
    from this socket.
*NN_STAT_ACCEPTED_CONNECTIONS*::
    The number of connections successfully established that were accepted
    by this socket.
*NN_STAT_DROPPED_CONNECTIONS*::
    The number of established connections that were dropped by this socket.
*NN_STAT_BROKEN_CONNECTIONS*::
    The number of established connections that were closed by this socket,
    typically due to protocol errors.
*NN_STAT_CONNECT_ERRORS*::
    The number of errors encountered by this socket trying to connect to
    a remote peer.
*NN_STAT_BIND_ERRORS*::
    The number of errors encountered by this socket trying to bind to
    a local address.
*NN_STAT_ACCEPT_ERRORS*::
    The number of errors encountered by this socket trying to accept a
    a connection from a remote peer.
*NN_STAT_CURRENT_CONNECTIONS*::
    The number of connections currently estabalished to this socket.
*NN_STAT_MESSAGES_SENT*::
    The number messages sent by this socket.
*NN_STAT_MESSAGES_RECEIVED*::
    The number messages received by this socket.
*NN_STAT_BYTES_SENT*::
    The number of bytes sent by this socket.
*NN_STAT_BYTES_RECEIVED*::
    The number of bytes received by this socket.


RETURN VALUE
------------
On success, the value of the statistic is returned, otherwise (uint64_t)-1
is returned.


ERRORS
------
*EINVAL*::
The statistic is invalid or unsupported.
*EBADF*::
The provided socket is invalid.
*ETERM*::
The library is terminating.


EXAMPLE
-------

----
val = nn_get_statistic (s, NN_STAT_MESSAGES_SENT);
if (val == 0)
    printf ("No messages have been sent yet.\n");
----

SEE ALSO
--------
<<nn_errno#,nn_errno(3)>>
<<nn_symbol#,nn_symbol(3)>>
<<nanomsg#,nanomsg(7)>>


AUTHORS
-------
link:mailto:<EMAIL>[Garrett D'Amore]

