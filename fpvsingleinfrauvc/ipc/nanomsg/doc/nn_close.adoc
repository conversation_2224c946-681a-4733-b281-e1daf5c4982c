nn_close(3)
===========

NAME
----
nn_close - close an SP socket


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_close (int 's');*


DESCRIPTION
-----------
Closes the socket 's'. Any buffered inbound messages that were not yet received
by the application will be discarded. The library will try to deliver any
outstanding outbound messages for the time specified by _NN_LINGER_ socket
option. The call will block in the meantime.


RETURN VALUE
------------
If the function succeeds zero is returned. Otherwise, -1 is
returned and 'errno' is set to to one of the values defined below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*EINTR*::
Operation was interrupted by a signal. The socket is not fully closed yet.
Operation can be re-started by calling _nn_close()_ again.


EXAMPLE
-------

----
int s = nn_socket (AF_SP, NN_PUB);
assert (s >= 0);
int rc = nn_close (s);
assert (rc == 0);
----


SEE ALSO
--------
<<nn_socket#,nn_socket(3)>>
<<nn_setsockopt#,nn_setsockopt(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

