nn_poll(3)
==========

NAME
----
nn_poll - poll a set of SP sockets for readability and/or writability


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_poll (struct nn_pollfd *fds, int nfds, int timeout);*


DESCRIPTION
-----------
The function checks a set of SP socket and reports whether it's possible to
send a message to the socket and/or receive a message from each socket.

'fds' argument is an array of nn_pollfd structures with 'nfds' argument
specifying the size of the array:

----
struct nn_pollfd {
    int fd;
    short events;
    short revents;
};
----

Each entry in the array represents an SP socket to check. 'events' field
specifies which events to check for. The value is a bitwise combination of
the following values:

*NN_POLLIN*::
Check whether at least one message can be received from the 'fd' socket without
blocking.

*NN_POLLOUT*::
Check whether at least one message can be sent to the 'fd' socket without
blocking.

After the function returns, 'revents' field contains bitwise combination of
NN_POLLIN and NN_POLLOUT according to whether the socket is readable or
writable.

'timeout' parameter specifies how long (in milliseconds) should the function
block if there are no events to report.

RETURN VALUE
------------
Upon successful completion, the number of nn_pollfds structures with events
signaled is returned. In case of timeout, return value is 0. In case of error,
-1 is returned and 'errno' is set the one of the values below.


ERRORS
------
*EBADF*::
Some of the provided sockets are invalid.
*EINTR*::
The operation was interrupted by delivery of a signal before the message was
sent.
*ETERM*::
The library is terminating.

NOTE
----
nn_poll is a convenience function. You can achieve same behaviour by using
NN_RCVFD and NN_SNDFD socket options. However, using the socket options
allows for usage that's not possible with nn_poll, such as simultaneous polling
for both SP and OS-level sockets, integration of SP sockets with external event
loops etc.

EXAMPLE
-------

----
struct nn_pollfd pfd [2];
pfd [0].fd = s1;
pfd [0].events = NN_POLLIN | NN_POLLOUT;
pfd [1].fd = s2;
pfd [1].events = NN_POLLIN;
rc = nn_poll (pfd, 2, 2000);
if (rc == 0) {
    printf ("Timeout!");
    exit (1);
}
if (rc == -1) {
    printf ("Error!");
    exit (1);
}
if (pfd [0].revents & NN_POLLIN) {
    printf ("Message can be received from s1!");
    exit (1);
}
----


SEE ALSO
--------
<<nn_socket#,nn_socket(3)>>
<<nn_getsockopt#,nn_getsockopt(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]

