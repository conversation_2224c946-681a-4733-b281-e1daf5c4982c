nn_connect(3)
=============

NAME
----
nn_connect - add a remote endpoint to the socket


SYNOPSIS
--------
*#include <nanomsg/nn.h>*

*int nn_connect (int 's', const char '*addr');*


DESCRIPTION
-----------
Adds a remote endpoint to the socket 's'. The library would then try to connect
to the specified remote endpoint.

The 'addr' argument consists of two parts as follows: 'transport'`://`'address'.
The 'transport' specifies the underlying transport protocol to use. The meaning
of the 'address' part is specific to the underlying transport protocol.

For the list of available transport protocols check the list on
<<nanomsg#,nanomsg(7)>> manual page.

Maximum length of the 'addr' parameter is specified by _NN_SOCKADDR_MAX_
defined in '<nanomsg/nn.h>' header file.

Note that nn_connect and <<nn_bind#,nn_bind(3)>> may be called multiple times
on the same socket thus allowing the socket to communicate with multiple
heterogeneous endpoints.

NOTE
----

Unlike with traditional BSD sockets, this function operates asynchronously,
and returns to the caller before the operation is complete.
As a result, attempts to send data or receive data on the socket may not
succeed until the underlying transport actually establishes the connection.
Further, the connection may be lost, without any notification to the caller.
The library will attempt to reconnect automatically in such an event.

RETURN VALUE
------------
If the function succeeds positive endpoint ID is returned. Endpoint ID can be
later used to remove the endpoint from the socket via <<nn_shutdown#,nn_shutdown(3)>>
function.

If the function fails negative value is returned and 'errno' is set to to one of
the values defined below.


ERRORS
------
*EBADF*::
The provided socket is invalid.
*EMFILE*::
Maximum number of active endpoints was reached.
*EINVAL*::
The syntax of the supplied address is invalid.
*ENAMETOOLONG*::
The supplied address is too long.
*EPROTONOSUPPORT*::
The requested transport protocol is not supported.
*ENODEV*::
Address specifies a nonexistent interface.
*ETERM*::
The library is terminating.


EXAMPLE
-------

----
s = nn_socket (AF_SP, NN_PUB);
eid1 = nn_connect (s, "ipc:///tmp/test.ipc");
eid2 = nn_connect (s, "tcp://server001:5560");
----


SEE ALSO
--------
<<nn_inproc#,nn_inproc(7)>>
<<nn_ipc#,nn_ipc(7)>>
<<nn_tcp#,nn_tcp(7)>>
<<nn_socket#,nn_socket(3)>>
<<nn_bind#,nn_bind(3)>>
<<nn_shutdown#,nn_shutdown(3)>>
<<nanomsg#,nanomsg(7)>>

AUTHORS
-------
link:mailto:<EMAIL>[Martin Sustrik]
link:mailto:<EMAIL>[Garrett D'Amore]

