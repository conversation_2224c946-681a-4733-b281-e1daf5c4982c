/*
    Copyright (c) 2012-2013 <PERSON>  All rights reserved.

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom
    the Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
    IN THE SOFTWARE.
*/

#ifndef NN_DNS_INCLUDED
#define NN_DNS_INCLUDED

#include "../../aio/fsm.h"

#include <stddef.h>

/*  Checks the hostname according to RFC 952 and RFC 1123.
    Returns 0 in case the it is valid. */
int nn_dns_check_hostname (const char *name, size_t namelen);

/*  Events generated by the DNS state machine. */
#define NN_DNS_DONE 1
#define NN_DNS_STOPPED 2

#if defined NN_HAVE_GETADDRINFO_A && !defined NN_DISABLE_GETADDRINFO_A
#include "dns_getaddrinfo_a.h"
#else
#include "dns_getaddrinfo.h"
#endif

struct nn_dns_result {
    int error;
    struct sockaddr_storage addr;
    size_t addrlen;
};

void nn_dns_init (struct nn_dns *self, int src, struct nn_fsm *owner);
void nn_dns_term (struct nn_dns *self);

int nn_dns_isidle (struct nn_dns *self);
void nn_dns_start (struct nn_dns *self, const char *addr, size_t addrlen,
    int ipv4only, struct nn_dns_result *result);
void nn_dns_stop (struct nn_dns *self);

#endif
