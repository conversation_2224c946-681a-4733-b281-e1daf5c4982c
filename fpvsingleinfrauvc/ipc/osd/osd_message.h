#ifndef OSD_MESSAGE_H_
#define OSD_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"


typedef enum  {
    OSD_SWITCH_OFF_CMD = 0,
    OSD_SWITCH_ON_CMD = 1,
    OSD_INFO_CMD = 2,
    OSD_LANGUAGE_CMD = 3,
} osd_cmd_e;


typedef struct 
{
    airvisen_ipc_message_head_s head;
    //目标经度
    int obj_lontitude;
    //目标纬度
    int obj_latitude;
    //目标海拔
    int obj_altitude;
    //目标距离
    int obj_distance;
    //飞行器经度
    int plane_lontitude;
    //飞行器纬度
    int plane_latitude;
    //飞行器海拔
    int plane_altitude;
    //跟踪状态
    int track_state;
    //变倍倍率
    int zoom_factor;
    //俯仰角度
    int pitch_angle;
    //偏航角度
    int yaw_angle;

}osd_message_s;





void airvisen_start_osd_ctrl_ipc();
void airvisen_stop_osd_ctrl_ipc();
void airvisen_start_osd_ctrl_client_ipc();
void airvisen_start_osd_ctrl_command(int zoom_factor);
int dispatch_osd_ctrl_message(int data_len, char *data);
int airvisen_recv_osd_ctrl_message(char *data, int data_len);
int airvisen_recv_osd_message(char *data, int data_len, int flags);

void airvisen_update_osd_request(osd_message_s *result);
void airvisen_show_osd_request();
void airvisen_hide_osd_request();
void airvisen_change_osd_language_request();

#ifdef __cplusplus
}
#endif

#endif /*OSD_MESSAGE_H_*/