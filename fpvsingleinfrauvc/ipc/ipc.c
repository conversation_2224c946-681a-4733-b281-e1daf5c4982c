#include "ipc.h"
#include "./include/nn.h"
#include "./include/pair.h"
//#include "testutil.h"
#include <stdio.h>
#include "airvisen_message.h"
/*  Tests IPC transport. */

//#define SOCKET_ADDRESS "ipc://airvisen.ipc"

/**
 * @param int sock: ipc sock
 * @param char *data: data that need to be send.
 */
int ipc_send_message (int sock, int data_len, char *data)
{
    //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    int rc;
    rc = nn_send (sock, data, data_len, NN_DONTWAIT);
    //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    if (rc < 0) {
    //printf("line:%d, file:%s\n", __LINE__, __FILE__);
        fprintf (stdout, "Failed to send:  [%d] \n",
            // nn_err_strerror (errno),
            (int) errno); 
    }
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    if (rc != (int)data_len) {
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
        fprintf (stdout, "Data to send is truncated: %d != %d \n",
            rc, (int) data_len);
    }
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
   //printf("rc:%d\n", rc);
    return rc;
}

/**
 * flags, 0 indicate wait , 1 NN_DONTWAIT
 */
int ipc_recv_message(int sock, char *data, int data_len, int flags)
{
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    int rc = 0;
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    rc = nn_recv(sock, data, data_len, flags);
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
    if (rc < 0) {

        if (errno == EAGAIN)
        {
           //printf("no message recv\n");
        }else{
            fprintf(stdout, "Failed to recv: [%d] \n",
            // nn_err_strerror (errno),
             (int) errno);

        }
    }
    
   //printf("line:%d, file:%s\n", __LINE__, __FILE__);
   //printf("rc:%d\n", rc);
    return rc;

}


int ipc_recv_message_wait(int sock, char *data, int data_len)
{
    int rc = 0;
    rc = nn_recv(sock, data, data_len, 0);
    if (rc < 0) {

        if (errno == EAGAIN)
        {
            //printf("no message recv\n");
        }else{
            fprintf (stderr, "Failed to recv: [%d] \n",
                        // nn_err_strerror (errno),
                        (int) errno);
        }
    }
    
    return rc;

}

#if 0
int dispatch_track_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case START_JPEG_STORE:{
            printf("Recv START_JPEG_STORE command\n");
        }
        break;  

        case STOP_JPEG_STORE:{
            printf("Recv STOP_JPEG_STORE command\n");
        }
        break;

        case START_VIDEO_RECORD:{
            printf("Recv START_VIDEO_RECORD command\n");
        }
        break;

        case STOP_VIDEO_RECORD:{
            printf("Recv STOP_VIDEO_RECORD command\n");
        }
        break;

    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        break;
    }
}

#endif

/**
 * ipc main proc
 */
int ipc_server_start(char *address)
{
    int sock;
    int rc;
    int i;

    //create
    sock = nn_socket (AF_SP, NN_PAIR);
    if (sock == -1) {
        fprintf (stderr, "Failed create socket: [%d]\n",
            // nn_err_strerror (errno),
            (int) errno);
    }

    //bind
    rc = nn_bind (sock, address);
    if(rc < 0) {
        fprintf (stderr, "Failed bind to \"%s\": [%d]\n",
            address,
            // nn_err_strerror (errno),
            (int) errno);
    }

#if 0
    //TODO THREAD
    airvisen_track_message_store_jpeg_s store_jpeg_msg = {0};
    store_jpeg_msg.cmd = START_JPEG_STORE;
    store_jpeg_msg.len = sizeof(airvisen_track_message_store_jpeg_s);

    for (;;)
    {
        
        #if 0
        size_t data_len;
        char *data = NULL;
        data_len = nn_recv(sock, data, NN_MSG, 0);
        if (rc < 0) {
            fprintf (stderr, "Failed to recv: %s [%d] \n",
                nn_err_strerror (errno),
                (int) errno);
        }
        nn_freemsg(data);
        #endif

        //dispatch message
        nn_sleep (1000);
        send_message(sock, sizeof(airvisen_track_message_store_jpeg_s), (char *)&store_jpeg_msg);

        
    }
#endif

    return sock;
}



/**
 * ipc main proc
 */
int ipc_client_start(char *address)
{
    int sock;
    int rc;
    int i;

    nn_sleep (200);
    //create
    sock = nn_socket (AF_SP, NN_PAIR);
    if (sock == -1) {
        fprintf (stderr, "Failed create socket: [%d]\n",
            // nn_err_strerror (errno),
            (int) errno);
    }

    printf("create  pair.\n");
    //connect
    rc = nn_connect (sock, address);
    if(rc < 0) {
        fprintf (stderr, "Failed connect to \"%s\": [%d]\n",
            address,
            // nn_err_strerror (errno),
            (int) errno);
    }

#if 0
    printf("connect  pair.\n");
    //TODO THREAD
    for (;;)
    {
        size_t data_len;
        char *data = NULL;
        data_len = nn_recv(sock, &data, NN_MSG, 0);
        if (rc < 0) {
            fprintf (stderr, "Failed to recv: %s [%d] \n",
                nn_err_strerror (errno),
                (int) errno);
        }

        printf("recv message from pair.\n");
        //dispatch message
        dispatch_track_message(data_len, data);

        nn_freemsg(data);
    }
#endif
    return sock;
}



void ipc_close(int sock)
{
    //close
    int rc = nn_close (sock);
    if ((rc != 0) && (errno != EBADF && errno != ETERM)) {
        fprintf (stderr, "Failed to close socket: [%d]\n",
            // nn_err_strerror (errno),
            (int) errno);
    }
}





