#!/bin/sh


load_usage()
{
	echo "Usage:  ./load_uvc_host.sh [ -r|-i]"
	echo "options:"
	echo "    -h    help information"
	echo "    -i    insmod all modules for uvc host"
	echo "    -r    remove all modules for uvc host"
	echo -e "for example: ./load_uvc_host.sh -i\n"
}

function stop_uvc_host()
{
	rmmod uvcvideo
	rmmod videobuf2_vmalloc
}

function start_uvc_host()
{
	insmod /lib/modules/videobuf2-vmalloc.ko
	insmod /lib/modules/uvcvideo.ko
}

b_arg_insmod=0
b_arg_remove=0

if [ $# -lt 1 ]; then
	load_usage;
	exit 0;
fi

for arg in $@
do
	case $arg in
		"-h")
			load_usage;
			exit;
			;;
		"-i")
			b_arg_insmod=1;
			;;
		"-r")
			b_arg_remove=1;
			;;
	esac
done

if [ $b_arg_remove -eq 1 ]; then
	stop_uvc_host;
fi

if [ $b_arg_insmod -eq 1 ]; then
	start_uvc_host;
fi
