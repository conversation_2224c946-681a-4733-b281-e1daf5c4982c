/**
* @file vs_mal_nn.h
* @brief Declaration of nn interface
* @details Declaration of nn interface
* <AUTHOR> npu group
* @date 2022-5-25
* @version v0.10
* @Copyright (C) 2022, Shanghai Visinex Technologies Co., Ltd. All rights reserved.
*/


#ifndef __VS_MAL_NN_H__
#define __VS_MAL_NN_H__

#include "vs_type.h"

#include "vs_nn_defines.h"

#ifdef __cplusplus
extern "C"{
#endif

/**
* @brief       NN runtime environment initlization
* @details     Init NPU hardware and neccessary resource for load and run network binary models.
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_init(vs_void_t);

/**
* @brief       Terminate the runtime driver
* @details     This function should be the last called by application and paired with vs_mal_nn_init
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_exit(vs_void_t);

/**
* @brief       Create a model in nn sdk
* @details     Create a model from memory, that mean sdk will hold the model after this action
* @param [in]  p_model_file, the network binary model file generated by IDE/Mapper tool
* @param [in]  p_model_name, the model name
* @param [out] p_model, model struct to show the detail information of this model
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_model_create(const vs_nn_mem_info_s *p_model_buf, const vs_char_t *p_model_name, vs_nn_model_s *p_model);

/**
* @brief       Get the parameter for model
* @details     Get the related parameter for model before verify;
* @param [in]  p_model, the model structure point
* @param [out]  p_nn_param, the parameter be used for verifying the model and run later
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_model_param_get(const vs_nn_model_s *p_model, vs_nn_model_param_s *p_nn_param);
/**
* @brief       Set the parameter of model
* @details     Set the parameter of verify and run the model
* @param [in]  p_model, the model structure point
* @param [in]  p_nn_param, set the parameter for verifying the model and run later
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_model_param_set(const vs_nn_model_s *p_model, const vs_nn_model_param_s *p_nn_param);
/**
* @brief       Verify the model
* @details     Verify the model in sdk internal, can this to optimize the model dependency
* @param [in]  p_model, the model structure point
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_model_verify(const vs_nn_model_s *p_model);

/**
* @brief       Invoke the model for process one inference
* @details     Invoke the model for inference, read input data, then write result to output
* @param [out] p_nn_handle, the task id of this process
* @param [in]  p_model, the task id of this process
* @param [in]  p_ivk_attr, the invoke task attributes detail
* @param [in]  block, app block here to wait the task done
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_invoke(vs_uint32_t               *p_nn_handle,
                            const vs_nn_model_s       *p_model,
                            const vs_nn_invoke_attr_s *p_ivk_attr,
                            vs_bool_t                 block);

/**
* @brief       Query a task status
* @details     Query a task is finished or not, can block there wait the task done
* @param [in]  p_nn_handle, task handle
* @param [out] p_finish, task status, finish or not
* @param [in]  block, wait there to task done.
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_query(const vs_uint32_t *p_nn_handle,
                           vs_bool_t         *p_finish,
                           vs_bool_t         block);

/**
* @brief       Destory a model from nn sdk
* @details     Destory the model and related resource in nnsdk
* @param [in]  p_model, context of this model being for destorying
* @return      0 call success
* @return      none 0 call failed, see ERROR_CODE for detail
* @par
*/
vs_int32_t vs_mal_nn_model_destroy(vs_nn_model_s *p_model);

#ifdef __cplusplus
}
#endif
#endif /*!<  __VS_MAL_NN_H__ */
