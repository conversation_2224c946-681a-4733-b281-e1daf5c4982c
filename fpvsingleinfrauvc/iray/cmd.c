#include "cmd.h"

int get_file_len(const char* p_path)
{
	int f_len = 0;
	if (NULL == p_path)
		return f_len;
	FILE* pf = fopen(p_path, "rb");
	if (NULL == pf)
		return f_len;
	fseek(pf, 0, SEEK_END); //�Ƚ�ָ��ƫ�Ƶ��ļ�β    
	f_len = ftell(pf);
	fclose(pf);
	return f_len;
}

uint16_t new_nuc_table[NUC_T_SIZE] = { 0 };
uint16_t new_kt[KT_SIZE] = { 0 };
int16_t new_bt[BT_SIZE] = { 0 };

void multi_point_calibration(uint16_t* correct_table, uint16_t* nuc_table, \
	uint16_t* org_kt, int16_t* org_bt, TempCalibParam_t* temp_calib_param)
{
	int i = 0;
	second_calibration_result_t second_cal_result = { new_kt,new_bt };

	second_calibration_param_t second_cal_param = { temp_calib_param->Ktemp, \
		temp_calib_param->Btemp, temp_calib_param->AddressCA,\
		   KT_SIZE, BT_SIZE, NUC_T_SIZE };
	second_cal_param.setting_temp_high = 415.9;
	second_cal_param.setting_temp_low = 123.9;
	second_cal_param.high_vtemp = 8400;
	second_cal_param.low_vtemp = 8400;
	second_cal_param.object_temp_high = 341.15;
	second_cal_param.object_temp_low = 104.5;

	float ems = 0.95;
	float hum = 0.5;
	float dist = 0.25;
	float ta = 25;
	float tu = 25;

	EnvCorrectParam env_cor_param[] = { { dist,ems,hum,ta,tu },\
	{ dist, ems, hum, ta, tu }, { dist,ems,hum,ta,tu } };


	MultiPointCalibTemp_t multi_point_temp[] = { { 6042.0 / 16 - 273.15,second_cal_param.setting_temp_low  },\
	{ 7776.5 / 16 - 273.15, 259.4  }, { 9828.8 / 16 - 273.15, second_cal_param.setting_temp_high } };
	MultiPointCalibNuc_t multi_point_nuc[] = { { 0,0 },{ 0,0}, { 0,0 } };

	MultiPointCalibParam_t multi_point_calib_param = { env_cor_param[0],
	   second_cal_param,8400 };
	MultiPointCalibArray_t multi_point_calib_array = { org_kt, org_bt, nuc_table, correct_table };
	TwoPointCalibResult_t two_point_calib_result = { new_kt, new_bt };
	new_ktbt_recal_double_point_calculate(&multi_point_calib_param, 4, &multi_point_calib_array, &two_point_calib_result);
	printf("new_kt[0]= %d,new_bt[0]= %d\n", two_point_calib_result.new_kt_array[0], two_point_calib_result.new_bt_array[0]);
	for (i = 0; i < sizeof(multi_point_temp) / sizeof(multi_point_temp[0]); i++)
	{
		MultiPointCalibParam_t multi_point_calib_param = { env_cor_param[i],
				  second_cal_param,8400 };

		multi_point_calc_user_defined_nuc(&multi_point_calib_param, 0, \
			& multi_point_calib_array, &two_point_calib_result, \
			& multi_point_temp[i], &multi_point_nuc[i]);
		printf("setting_nuc=%d output_nuc=%d\n", multi_point_nuc[i].setting_nuc / 2, multi_point_nuc[i].output_nuc / 2);
	}

	multi_point_calc_new_nuc_table(nuc_table, multi_point_nuc,
		sizeof(multi_point_temp) / sizeof(multi_point_temp[0]), new_nuc_table);

	for (i = 0; i < NUC_T_SIZE; i++)
	{
		printf("new_nuc_table[%d]= %d %d\n", i, nuc_table[i], new_nuc_table[i]);
	}
	printf("new_nuc_table[%d]= %d\n", 0, new_nuc_table[0]);
}


void multi_point_calibration_in_change_lens(uint16_t* correct_table, uint16_t* nuc_table, \
	uint16_t* org_kt, int16_t* org_bt, TempCalibParam_t* temp_calib_param)
{
	uint32_t correct_table_len = 0;

	KtbtCoefficient_t ktbt_coef = { 0 };
	FILE* fp = NULL;
	int i = 0;

	//��ȡ���ϵ��
	double p[4];
	fp = fopen("./KT_Low_Factor_230106.bin", "rb");
	fread(p, 32, 1, fp);
	fclose(fp);
	ktbt_coef.kt_coef.P0 = p[0];
	ktbt_coef.kt_coef.P1 = p[1];
	ktbt_coef.kt_coef.P2 = p[2];
	ktbt_coef.kt_coef.P3 = p[3];
	fp = fopen("./BT_Low_Factor_230106.bin", "rb");
	fread(p, 32, 1, fp);
	fclose(fp);
	ktbt_coef.bt_coef.P0 = p[0];
	ktbt_coef.bt_coef.P1 = p[1];
	ktbt_coef.bt_coef.P2 = p[2];
	ktbt_coef.bt_coef.P3 = p[3];

	printf("kt_coef.P0 = %.15f\n", ktbt_coef.kt_coef.P0);
	printf("kt_coef.P1 = %.15f\n", ktbt_coef.kt_coef.P1);
	printf("kt_coef.P2 = %.15f\n", ktbt_coef.kt_coef.P2);
	printf("kt_coef.P3 = %.15f\n", ktbt_coef.kt_coef.P3);
	printf("bt_coef.P0 = %.15f\n", ktbt_coef.bt_coef.P0);
	printf("bt_coef.P1 = %.15f\n", ktbt_coef.bt_coef.P1);
	printf("bt_coef.P2 = %.15f\n", ktbt_coef.bt_coef.P2);
	printf("bt_coef.P3 = %.15f\n", ktbt_coef.bt_coef.P3);

	second_calibration_param_t second_cal_param = { temp_calib_param->Ktemp, \
		temp_calib_param->Btemp, temp_calib_param->AddressCA,\
	   KT_SIZE, BT_SIZE, NUC_T_SIZE };
	/*second_calibration_param_t second_cal_param = { -3996, 4893, 2330, \
	   KT_SIZE, BT_SIZE, NUC_T_SIZE };*/
	second_cal_param.setting_temp_high = 450;
	second_cal_param.setting_temp_low = 50;
	second_cal_param.high_vtemp = 7434;
	second_cal_param.low_vtemp = 7446;
	second_cal_param.object_temp_high = 431.975;
	second_cal_param.object_temp_low = 49.9125;

	uint16_t vtemp[3] = { 7446, 7441, 7434 };

	MultiPointCalibArray_t multi_point_calib_array = { (uint16_t*)org_kt, (int16_t*)org_bt, (uint16_t*)nuc_table, (uint16_t*)correct_table };

	float ems = 1;
	float hum = 0.5;
	float dist = 25;
	float ta = 25;
	float tu = 25;
	EnvCorrectParam env_cor_param[] = { { dist,ems,hum,ta,tu },\
	{ dist, ems, hum, ta, tu }, { dist,ems,hum,ta,tu } };
	AdvancedMultiPointCalibParam_t multi_point_calib_param = { env_cor_param[2], env_cor_param[0],
	   second_cal_param };
	MultiPointCalibTemp_t multi_point_temp[] = { { second_cal_param.object_temp_low,second_cal_param.setting_temp_low  },\
	{ 237.4125, 250  }, { second_cal_param.object_temp_high, second_cal_param.setting_temp_high } };

	MultiPointCalibNuc_t multi_point_nuc[3] = { { 0,0 },{ 0,0}, { 0,0 } };

	TwoPointCalibResult_t two_point_calib_result = { new_kt, new_bt };

	//�õ��µ�KTBT
	new_ktbt_recal_double_point_calculate_in_change_lens(&multi_point_calib_param, &ktbt_coef, \
		& multi_point_calib_array, &two_point_calib_result);
	fp = fopen("new_kt_table.csv", "wb");
	for (i = 0; i < KT_SIZE; i++)
	{
		fprintf(fp, "%d\n", two_point_calib_result.new_kt_array[i]);
	}
	fclose(fp);

	fp = fopen("new_bt_table.csv", "wb");
	for (i = 0; i < BT_SIZE; i++)
	{
		fprintf(fp, "%d\n", two_point_calib_result.new_bt_array[i]);
	}
	fclose(fp);

	for (int i = 0; i < sizeof(multi_point_temp) / sizeof(multi_point_temp[0]); i++)
	{
		MultiPointCalibParam_t multi_point_calib_param = { env_cor_param[i],
				  second_cal_param,vtemp[i] };

		multi_point_calc_user_defined_nuc_in_change_lens(&multi_point_calib_param, \
			& multi_point_calib_array, &two_point_calib_result, \
			& multi_point_temp[i], &multi_point_nuc[i]);
		printf("setting_nuc=%f output_nuc=%f\n", (double)multi_point_nuc[i].setting_nuc / 2, (double)multi_point_nuc[i].output_nuc / 2);
	}
	multi_point_calc_new_nuc_table_in_change_lens((uint16_t*)nuc_table, multi_point_nuc, 3, new_nuc_table);
	fp = fopen("new_nuct_array.csv", "wb");
	for (i = 0; i < NUC_T_SIZE; i++)
	{
		fprintf(fp, "%d\n", new_nuc_table[i]);
	}
	fclose(fp);
}


//command selection
void command_sel(StreamFrameInfo_t* handle, int cmd_type)
{
	int rst = 0;
	uint8_t id_data[100] = { 0 };
	float zoom_factor = 0;
	uint16_t temp_data = 0;
	IrcmdPoint_t point_1 = { 320,256 };
	IrcmdLine_t line1 = { 100, 100, 300, 300 };
	IrcmdRect_t rect1 = { 0, 0, 383, 287 };
	TpdLineRectTempInfo_t line1_temp_info = { 0 };
	TpdLineRectTempInfo_t rect1_temp_info = { 0 };
	FrameTempInfo_t frame_temp_info = { 0 };
	IrcmdPoint_t point_pos = { 100,100 };
	uint16_t point_temp_value = 0;
	uint16_t value=0;
	uint8_t* pbyData = NULL;
	FILE* fp = NULL;
	uint8_t oem_data[480] = { 0 };

	static uint16_t kt_array[1201] = { 0 };
	static int16_t bt_array[1201] = { 0 };
	static uint16_t nuct_array[8192] = { 0 };
	TempCalibParam_t temp_calib_param;
	uint32_t file_len = 0;
	uint16_t correct_table[4 * 14 * 64 + 128];				//��������������
	switch (cmd_type)
	{
	case 0:
		basic_get_device_info(handle->ircmd_handle, BASIC_DEV_INFO_GET_PN, id_data);
		printf("get device PN:");
		for (int j = 0; j < 8; j++)
		{
			printf("0x%x ", id_data[j]);
		}
		printf("\n");
		break;
	case 1:
		basic_restore_def_algorithm_param(handle->ircmd_handle);
		printf("restore default algorithm params\n");
		break;
	case 2:
		basic_algorithm_param_save(handle->ircmd_handle);
		printf("save algorithm params\n");
		break;
	case 3:
		basic_y16_preview(handle->ircmd_handle, BASIC_Y16_MODE_YUV);
		printf("y16 preview mode YUV\n");
		break;
	case 4:
		basic_preview_yuv_format_set(handle->ircmd_handle, BASIC_PREVIEW_UYVY);
		printf("preview yuv format set:YUYV\n");
		break;
	case 5:
		zoom_factor = 1.5;
		basic_zoom_center_factor_set(handle->ircmd_handle, zoom_factor);
		printf("zoom_center_up\n");
		break;
	case 6:
		value = 3;
		basic_image_mirror_flip(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("image_mirror_flip\n");
		break;
	case 7:
		value = 3;
		basic_image_params_dde(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("image_params_dde\n");
		break;
	case 8:
		value = 200;
		basic_image_params_brightness(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("image_params_dde\n");
		break;
	case 9:
		value = 200;
		basic_image_params_contrast(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("image_params_contrast\n");
		break;
	case 10:
		basic_auto_shutter_onoff(handle->ircmd_handle, GET_PARAMS_STA, &value);
		printf("auto_shutter_onoff\n");
		printf("value =%d\n", value);
		break;
	case 11:
		value = 6;
		basic_auto_shutter_min_interval(handle->ircmd_handle, GET_PARAMS_STA, &value);
		printf("auto_shutter_min_interval\n");
		printf("value =%d\n", value);
		break;
	case 12:
		value = 6;
		basic_auto_shutter_max_interval(handle->ircmd_handle, GET_PARAMS_STA, &value);
		printf("auto_shutter_max_interval\n");
		printf("value =%d\n", value);
		break;
	case 13:
		value = 15;
		basic_auto_shutter_vtemp_threshold(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("basic_auto_shutter_vtemp_threshold\n");
		break;
	case 14:
		basic_shutter_update(handle->ircmd_handle);
		printf("Manual shutter\n");
		break;
	case 15:
		value = 6;
		basic_pseudo_color(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("pseudo color set:6\n");
		break;
	case 16:
		basic_tpd_get_point_temp_info(handle->ircmd_handle, point_pos, &point_temp_value);
		printf("tpd_get_point_temp_info:%.2f\n", (point_temp_value / 16 - 273.15));
		break;
	case 17:
		basic_tpd_get_line_temp_info(handle->ircmd_handle, line1, &line1_temp_info);
		printf("tpd_get_line_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
			line1_temp_info.min_temp_point.x, line1_temp_info.min_temp_point.y, \
			(line1_temp_info.temp_info_value.min_temp / 16 - 273.15), \
			line1_temp_info.max_temp_point.x, line1_temp_info.max_temp_point.y, \
			(line1_temp_info.temp_info_value.max_temp / 16 - 273.15));
		break;
	case 18:
		basic_tpd_get_rect_temp_info(handle->ircmd_handle, rect1, &rect1_temp_info);
		printf("tpd_get_rect_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
			rect1_temp_info.min_temp_point.x, rect1_temp_info.min_temp_point.y, \
			(rect1_temp_info.temp_info_value.min_temp / 16 - 273.15), \
			rect1_temp_info.max_temp_point.x, rect1_temp_info.max_temp_point.y, \
			(rect1_temp_info.temp_info_value.max_temp / 16 - 273.15));
		break;
	case 19:
		basic_tpd_get_frame_temp_info(handle->ircmd_handle, &frame_temp_info);
		printf("tpd_get_frame_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
			frame_temp_info.min_temp_point.x, frame_temp_info.min_temp_point.y, \
			(frame_temp_info.min_temp / 16 - 273.15), \
			frame_temp_info.max_temp_point.x, frame_temp_info.max_temp_point.y, \
			(frame_temp_info.max_temp / 16 - 273.15));
		break;
	case 20:
		value = 1;
		basic_prop_tpd_sel_gain(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("basic_prop_tpd_sel_gain\n");
		break;
	case 21:
		adv_image_params_agc_mode(handle->ircmd_handle, GET_PARAMS_STA, &value);
		printf("advanced_image_params_agc_mode\n");
		printf("value =%d\n", value);
		break;
	case 22:
		adv_rmcover_clear_data(handle->ircmd_handle);
		printf("clear rmcover data\n");
		break;
	case 23:
		adv_dpc_auto_calc(handle->ircmd_handle);
		printf("dpc auto calc\n");
		break;
	case 24:
		adv_dpc_add_point(handle->ircmd_handle, point_1);
		printf("add point as dpc\n");
		break;
	case 25:
		adv_dpc_remove_point(handle->ircmd_handle, point_1);
		printf("remove point from dpc\n");
		break;
	case 26:
		adv_dpc_save(handle->ircmd_handle);
		printf("dpc save to flash\n");
		break;
	case 27:
		adv_dpc_clear_data(handle->ircmd_handle);
		printf("clean dpc data from flash\n");
		break;
	case 28:
		temp_data = 300;
		adv_tpd_ktbt_recal_1point(handle->ircmd_handle, temp_data);
		printf(" recalculate the kt_bt by 1 point's temperature\n");
		break;
	case 29:
		temp_data = 300;
		adv_tpd_ktbt_recal_2point(handle->ircmd_handle, TPD_KTBT_RECAL_P1, temp_data);
		printf(" recalculate the kt_bt by 2 point's temperature\n");
		break;
	case 30:
		adv_restore_default_cfg(handle->ircmd_handle, ADV_CFG_TPD);
		printf("restore default tpd params\n");
		break;
	case 31:
		pbyData = (uint8_t*)malloc(8192);
		adv_read_error_log(handle->ircmd_handle, pbyData);
		printf("read fw error log\n");
		fp = fopen("aim_image.bin", "wb");
		fwrite(pbyData, 1, 8192, fp);
		fclose(fp);
		break;
	case 32:
		adv_oem_erase(handle->ircmd_handle);
		printf("erase data in flash's OEM section\n");
		break;
	case 33:
		rst = adv_oem_read(handle->ircmd_handle, oem_data);
		printf("rst = %d\n", rst);
		printf("read data in flash's OEM section\n");
		for (int i = 0; i < 8; i++)
		{
			printf("oem_data[%d] = %d\n", i, oem_data[i]);
		}
		break;
	case 34:
		for (int i = 0; i < 8; i++)
		{
			oem_data[i]= i;
		}
		adv_oem_write(handle->ircmd_handle, oem_data);
		printf("write data in flash's OEM section\n");
		break;
	case 35:
		basic_preview_mode_select(handle->ircmd_handle, BASIC_USB_MODE, BASIC_FRAME_COMPOSITE_DATA);
		printf("basic_preview_mode_select\n");
		break;
	case 36:
		basic_preview_mode_select(handle->ircmd_handle, BASIC_USB_MODE, BASIC_SINGLE_IMAGE_OR_TEMP);
		printf("basic_preview_mode_select\n");
		break;
	case 37:
		adv_rmcover_auto_calc(handle->ircmd_handle);
		printf("auto calibrate cover\n");
		break;
	case 38:
		value = 1;
		basic_prop_tpd_sel_gain(handle->ircmd_handle, SET_PARAMS_STA, &value);
		printf("set_prop_tpd_params to low gain\n");
		adv_restore_default_cfg(handle->ircmd_handle, ADV_CFG_TPD);
		basic_get_tpd_kt_array(handle->ircmd_handle, (uint8_t*)kt_array);
		printf("get_tpd_kt_array completed\n");
		printf("kt_array[0]=%d kt_array[1200]=%d\n", kt_array[0], kt_array[1200]);
		basic_get_tpd_bt_array(handle->ircmd_handle, (uint8_t*)bt_array);
		printf("get_tpd_bt_array completed\n");
		printf("bt_array[0]=%d bt_array[1200]=%d\n", bt_array[0], bt_array[1200]);
		basic_get_tpd_nuc_t_array(handle->ircmd_handle, (uint8_t*)nuct_array);
		printf("get_tpd_nuc_t_array completed\n");
		printf("nuct_array[0]=%d nuct_array[8191]=%d\n", nuct_array[0], nuct_array[8191]);
		basic_get_tpd_calib_param(handle->ircmd_handle, &temp_calib_param);
		printf("get_tpd_calib_param completed\n");
		printf("Ktemp=%d Btemp=%d AddressCA=%d\n", temp_calib_param.Ktemp, temp_calib_param.Btemp, temp_calib_param.AddressCA);
		break;
	case 39:
		file_len = get_file_len("tau_H.bin");
		printf("file_len=%d\n", file_len);
		fp = fopen("tau_H.bin", "rb");
		fread(correct_table, 1, file_len, fp);
		fclose(fp);
		multi_point_calibration(correct_table, nuct_array, kt_array, bt_array, &temp_calib_param);
		printf("finish multi_point_calibration\n");
		break;
	case 40:
		basic_set_tpd_kt_array(handle->ircmd_handle, (uint8_t*)new_kt);
		printf("set_tpd_kt_array completed new_kt[0]=%d new_kt[1200]=%d\n", new_kt[0],new_kt[1200]);
		basic_set_tpd_bt_array(handle->ircmd_handle, (uint8_t*)new_bt);
		printf("set_tpd_bt_array completed new_bt[0]=%d new_bt[1200]=%d\n", new_bt[0] ,new_bt[1200]);
		basic_set_tpd_nuc_t_array(handle->ircmd_handle, (uint8_t*)new_nuc_table);
		printf("set_tpd_bt_array completed new_nuc_table[0]=%d new_nuc_table[8191]=%d\n", new_nuc_table[0],new_nuc_table[8191]);
		basic_get_tpd_calib_param(handle->ircmd_handle, &temp_calib_param);
		printf("get_tpd_calib_param completed\n");
		printf("Ktemp=%d Btemp=%d AddressCA=%d\n", temp_calib_param.Ktemp, temp_calib_param.Btemp, temp_calib_param.AddressCA);
		break;
	case 41:
		//Must be called first "case 38"
		file_len = get_file_len("tau_L.bin");
		printf("file_len=%d\n", file_len);
		fp = fopen("tau_L.bin", "rb");
		fread(correct_table, 1, file_len, fp);
		fclose(fp);
		multi_point_calibration_in_change_lens(correct_table, nuct_array, kt_array, bt_array ,&temp_calib_param);
		printf("finish multi_point_calibration\n");
		break;
	default:
		break;
	}
}

//command thread function
void* cmd_function(void* threadarg)
{

	printf("open camera cmd_function\n");
	int cmd = 0;
	while (cmd != 999)
	{
		//scanf("%d", &cmd);
		//printf(" ------ run command ------\n");
		cmd = 17;
		command_sel(((StreamFrameInfo_t*)threadarg), cmd);
	}
	printf("cmd thread exit!!\n");
	return NULL;
}



int cmd_measure_point_temperature(StreamFrameInfo_t* handle, IrcmdPoint_t *point_pos, uint16_t *point_temp_value)
{
	IrcmdError_t ret = basic_tpd_get_point_temp_info(handle->ircmd_handle, *point_pos, point_temp_value);
	if (ret != IRCMD_SUCCESS)
	{
		printf("tpd_get_point_temp_info error\n");
		return ret;
	}
	printf("tpd_get_point_temp_info:%.2f\n", (*point_temp_value / 16 - 273.15));
	return ret;

}

int cmd_measure_line_temperature(StreamFrameInfo_t* handle, IrcmdLine_t *line1 , TpdLineRectTempInfo_t *line1_temp_info)
{
	IrcmdError_t ret = basic_tpd_get_line_temp_info(handle->ircmd_handle, *line1, line1_temp_info);
	if (ret != IRCMD_SUCCESS)
	{
		printf("tpd_get_line_temp_info error\n");
		return ret;
	}

	printf("tpd_get_line_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
		line1_temp_info->min_temp_point.x, line1_temp_info->min_temp_point.y, \
		(line1_temp_info->temp_info_value.min_temp / 16 - 273.15), \
		line1_temp_info->max_temp_point.x, line1_temp_info->max_temp_point.y, \
		(line1_temp_info->temp_info_value.max_temp / 16 - 273.15));

	return ret;
}


int cmd_measure_rect_temperature(StreamFrameInfo_t* handle, IrcmdRect_t *rect1, TpdLineRectTempInfo_t *rect1_temp_info)
{

	IrcmdError_t ret = basic_tpd_get_rect_temp_info(handle->ircmd_handle, *rect1, rect1_temp_info);
	if (ret != IRCMD_SUCCESS)
	{
		printf("tpd_get_rect_temp_info failed\n");
		return ret;
	}

	printf("tpd_get_rect_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
		rect1_temp_info->min_temp_point.x, rect1_temp_info->min_temp_point.y, \
		(rect1_temp_info->temp_info_value.min_temp / 16 - 273.15), \
		rect1_temp_info->max_temp_point.x,rect1_temp_info->max_temp_point.y, \
		(rect1_temp_info->temp_info_value.max_temp / 16 - 273.15));
	

	return ret;

}


int cmd_measure_frame_temperature(StreamFrameInfo_t* handle, FrameTempInfo_t *frame_temp_info)
{

	IrcmdError_t ret = basic_tpd_get_frame_temp_info(handle->ircmd_handle, frame_temp_info);
	if (ret != IRCMD_SUCCESS)
	{
		printf("tpd_get_frame_temp_info failed!\n");
		return ret;
	}
	printf("tpd_get_frame_temp_info:min(%d,%d):%.2f, max(%d,%d):%.2f\n", \
		frame_temp_info->min_temp_point.x, frame_temp_info->min_temp_point.y, \
		(frame_temp_info->min_temp / 16 - 273.15), \
		frame_temp_info->max_temp_point.x, frame_temp_info->max_temp_point.y, \
		(frame_temp_info->max_temp / 16 - 273.15));

	return ret;
}




int cmd_ctrl_shutter_onoff(StreamFrameInfo_t* handle, uint16_t *value)
{

	IrcmdError_t ret = basic_auto_shutter_onoff(handle->ircmd_handle, SET_PARAMS_STA, value);
	if (ret != IRCMD_SUCCESS)
	{
		printf("basic_auto_shutter_onoff failed!\n");
		return ret;
	}
	printf("auto_shutter_onoff\n");
	printf("value =%d\n", value);
	return ret;

}


int cmd_ctrl_pseduo_color(StreamFrameInfo_t* handle, uint16_t *value)
{
	IrcmdError_t ret = basic_pseudo_color(handle->ircmd_handle, SET_PARAMS_STA, value);
	if (ret != IRCMD_SUCCESS)
	{
		printf("basic_pseudo_color failed!\n");
		return ret;
	}
	printf("basic_pseudo_color\n");
}