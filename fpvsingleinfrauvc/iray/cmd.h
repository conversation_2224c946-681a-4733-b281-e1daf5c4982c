#ifndef _CMD_H_
#define _CMD_H_

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#if defined(_WIN32)
#include <io.h>
#elif defined(linux) || defined(unix)
#include <unistd.h>
#endif

#include "libiruvc.h"
#include "data.h"

//select the command
//void command_sel(StreamFrameInfo_t* handle1, StreamFrameInfo_t* handle2, int cmd_type);
void command_sel(StreamFrameInfo_t* handle, int cmd_type);

//command thread, get the input and select the command
void* cmd_function(void* threadarg);


int cmd_measure_point_temperature(StreamFrameInfo_t* handle, IrcmdPoint_t *point_pos, uint16_t *point_temp_value);
int cmd_measure_line_temperature(StreamFrameInfo_t* handle, IrcmdLine_t *line1 , TpdLineRectTempInfo_t *line1_temp_info);
int cmd_measure_rect_temperature(StreamFrameInfo_t* handle, IrcmdRect_t *rect1, TpdLineRectTempInfo_t *rect1_temp_info);
int cmd_measure_frame_temperature(StreamFrameInfo_t* handle, FrameTempInfo_t *frame_temp_info);
int cmd_ctrl_shutter_onoff(StreamFrameInfo_t* handle, uint16_t *value);
int cmd_ctrl_pseduo_color(StreamFrameInfo_t* handle, uint16_t *value);

#endif


