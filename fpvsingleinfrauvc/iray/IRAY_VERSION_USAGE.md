# IRAY版本选择使用说明

这个Makefile支持在mini1和mini2两个版本之间切换。

## 版本差异

### Mini1 (默认)
- 头文件路径: `iray/include/`
- 库文件路径: `iray/lib/`
- 库文件: libiri2c.so, libircmd.so, libiruvc.so, libirprocess.so, libirparse.so, libirtemp.so

### Mini2
- 头文件路径: `../mini2/include/`
- 库文件路径: `../mini2/lib/`
- 库文件: libircmd.so, libiruvc.so, libirparse.so

## 使用方法

### 1. 使用Mini1（默认方式）
```bash
cd fpvsingleinfrauvc/iray
make
```

### 2. 使用Mini2
有以下几种方式：

#### 方式1：命令行参数（推荐）
```bash
cd fpvsingleinfrauvc/iray
make USE_IRAY_MINI2=1
```

#### 方式2：环境变量
```bash
cd fpvsingleinfrauvc/iray
export USE_IRAY_MINI2=1
make
```

#### 方式3：永久设置
编辑Makefile，找到这两行：
```makefile
# 如果要永久使用mini2，取消下面这行的注释
# USE_IRAY_MINI2 = 1
```

将第二行的注释去掉：
```makefile
# 如果要永久使用mini2，取消下面这行的注释
USE_IRAY_MINI2 = 1
```

然后直接使用 `make` 命令即可。

## 清理编译文件
```bash
make clean
```

## 验证当前使用的版本
可以通过以下命令查看当前配置：
```bash
# 查看默认配置
make -n | grep -E "(-I.*include|-L.*lib)"

# 查看mini2配置
make USE_IRAY_MINI2=1 -n | grep -E "(-I.*include|-L.*lib)"
```

## 注意事项
1. 确保对应的头文件和库文件存在于指定路径
2. mini2的库文件比mini1少，这是正常的
3. 切换版本后建议先执行 `make clean` 再重新编译
