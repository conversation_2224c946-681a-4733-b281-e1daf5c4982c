#ifndef SAMPLE_H_
#define SAMPLE_H_

#if defined(_WIN32)
#include <Windows.h>
#elif defined(linux) || defined(unix)
#include <unistd.h>
#include <sys/time.h>
#include <sys/resource.h>
#endif

#include <stdio.h>

#include "cmd.h"
#include "camera.h"
#include "display.h"
#include "sample_version.h"

typedef enum {
	DEBUG_PRINT = 0,
	ERROR_PRINT,
	NO_PRINT,
}log_level_t;

typedef void (*frame_callback_func)(void*, unsigned int);

#define USER_FUNCTION_CALLBACK

//#define IMAGE_AND_TEMP_OUTPUT	//normal mode:get 1 image frame and temp frame at the same time eg:640*1024
#define IMAGE_OUTPUT	//only image frame eg:640*512
//#define TEMP_OUTPUT		//only temp frame eg:640*512

int start_iray_proc(void *callback);


int measure_point_temperature(IrcmdPoint_t *point_pos, uint16_t *point_temp_value);
int measure_line_temperature(IrcmdLine_t *line1 , TpdLineRectTempInfo_t *line1_temp_info);
int measure_rect_temperature(IrcmdRect_t *rect1, TpdLineRectTempInfo_t *rect1_temp_info);
int measure_frame_temperature(FrameTempInfo_t *frame_temp_info);
int ctrl_shutter_onoff(uint16_t *value);
int ctrl_pseduo_color(uint16_t *value);
int execute_command(const char *command);
#endif

