/**
 * @file    uvc_camera.c
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <unistd.h>

#include "uvc_util.h"

vs_char_t *vs_uvc_v4l2_pixelformat_str_get(vs_uint32_t pixelformat)
{
    static vs_char_t pixelformat_buf[64] = {0};

    sprintf((char *)pixelformat_buf, "%c%c%c%c",
                pixelformat & 0xFF,
                (pixelformat >> 8) & 0xFF,
                (pixelformat >> 16) & 0xFF,
                (pixelformat >> 24) & 0xFF);
    return pixelformat_buf;
}

vs_uint64_t vs_uvc_util_time_get(vs_void_t)
{//get ms
    vs_uint64_t time_now_ms = 0;
    struct timeval cur_timeval;

    gettimeofday(&cur_timeval, NULL);
    time_now_ms = cur_timeval.tv_sec * 1000 + cur_timeval.tv_usec / 1000;

    return time_now_ms;
}

vs_char_t *vs_uvc_util_time_get_str(vs_void_t)
{//get ms
    static vs_char_t time_str[256] = {0};
    struct timeval cur_timeval;

    memset(time_str, 0, sizeof(time_str));
    gettimeofday(&cur_timeval, NULL);
    sprintf((char *)time_str, "sec %lu, ms %lu ",cur_timeval.tv_sec, cur_timeval.tv_usec / 1000);

    return time_str;
}

