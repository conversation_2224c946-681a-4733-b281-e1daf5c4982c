#ifndef UVC_H_
#define UVC_H_

#include "vs_type.h"
#include "vs_mal_vbm.h"
#include "sample_common.h"


typedef struct vs_uvc_player_info {
    //player msg
    vs_int32_t                  vdec_chnnum;
    vs_int32_t                  vdec_chnid;
    vs_payload_type_e           decode_type;
    vs_size_s                   disp_size;
    sample_vdec_cfg_s           vdec_config;

    vs_int32_t                  vpp_grpid;
    vs_int32_t                  vpp_chnid;
    vs_int32_t                  vpp_fhd_chnid;
    vs_bool_t                   vpp_chn_enable[VPP_MAX_PHYCHN_NUM];

    vs_uint32_t                 vo_chnid;
    sample_vo_cfg_s             vo_config;
} vs_uvc_player_info_s;


typedef enum vs_uvc_framebuf_status {
    E_UVC_FRAMEBUF_STATUS_RELEASE = 0,
    E_UVC_FRAMEBUF_STATUS_ACQUIRE,
    E_UVC_FRAMEBUF_STATUS_MAX
} vs_uvc_framebuf_status_e;

typedef struct vs_uvc_framebuffer {
    vs_void_t                   *start;
    vs_uint32_t                 length;

    vs_video_frame_info_s       frame_info;

    vs_uvc_framebuf_status_e    status;
} vs_uvc_framebuffer_s;


typedef struct vs_uvc_camera_info {
    vs_char_t                   dev_path[256];
    vs_int32_t                  fd;
    vs_uint32_t                 width;
    vs_uint32_t                 height;
    vs_uint32_t                 fps;
    vs_uint32_t                 pixelformat;
    vs_uint32_t                 mem_type;

    pthread_t                   uvc_camera_td;
    vs_uint32_t                 is_stop_uvc_camera;

    VB_POOL                     buf_poolid;
    vs_uint32_t                 buf_count;
    vs_uvc_framebuffer_s        *buf;
    vs_int32_t                  buf_cur_index;
    vs_uint32_t                 buf_max_size;

    vs_uvc_player_info_s        player;

#ifdef VIDEO_ZOOM_ENABLED
    //todo, should be float.
    vs_uint32_t                 zoom_factor;
#endif
} vs_uvc_camera_info_s;



#endif