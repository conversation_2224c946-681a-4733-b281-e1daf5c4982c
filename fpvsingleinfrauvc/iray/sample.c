#include "sample.h"

#ifdef __cplusplus
extern "C" {
#endif

#include <sys/stat.h>
#include <stdio.h>
#include <stdlib.h>

int frame_idx = 0;

frame_callback_func frame_callback = NULL;
//load the stream frame info
void load_stream_frame_info(StreamFrameInfo_t* stream_frame_info)
{
#if defined(IMAGE_AND_TEMP_OUTPUT)
    {
        stream_frame_info->image_info.width = stream_frame_info->camera_param.width;
        stream_frame_info->image_info.height = stream_frame_info->camera_param.height / 2;
        stream_frame_info->image_info.rotate_side = NO_ROTATE;
        stream_frame_info->image_info.mirror_flip_status = STATUS_NO_MIRROR_FLIP;
        stream_frame_info->image_info.pseudo_color_status = PSEUDO_COLOR_OFF;
        stream_frame_info->image_info.img_enhance_status = IMG_ENHANCE_OFF;
        stream_frame_info->image_info.input_format = INPUT_FMT_YUV422; //only Y14 or Y16 mode can use enhance and pseudo color
        stream_frame_info->image_info.output_format = OUTPUT_FMT_BGR888; //if display on opencv,please select BGR888

        stream_frame_info->temp_info.width = stream_frame_info->camera_param.width;
        stream_frame_info->temp_info.height = stream_frame_info->camera_param.height / 2;
        stream_frame_info->temp_info.rotate_side = NO_ROTATE;
        stream_frame_info->temp_info.mirror_flip_status = STATUS_NO_MIRROR_FLIP;
        stream_frame_info->image_byte_size = stream_frame_info->image_info.width * stream_frame_info->image_info.height * 2;
        stream_frame_info->temp_byte_size = stream_frame_info->image_info.width * stream_frame_info->image_info.height * 2;//no temp frame input
    }
#elif defined(IMAGE_OUTPUT) || defined(TEMP_OUTPUT)
    stream_frame_info->image_info.width = stream_frame_info->camera_param.width;
    stream_frame_info->image_info.height = stream_frame_info->camera_param.height;
    stream_frame_info->image_info.rotate_side = NO_ROTATE;
    stream_frame_info->image_info.mirror_flip_status = STATUS_NO_MIRROR_FLIP;
    stream_frame_info->image_info.pseudo_color_status = PSEUDO_COLOR_OFF;
    stream_frame_info->image_info.img_enhance_status = IMG_ENHANCE_OFF;
    stream_frame_info->image_info.input_format = INPUT_FMT_YUV422; //only Y14 or Y16 mode can use enhance and pseudo color
    stream_frame_info->image_info.output_format = OUTPUT_FMT_BGR888; //if display on opencv,please select BGR888

    stream_frame_info->image_byte_size = stream_frame_info->image_info.width * stream_frame_info->image_info.height * 2;
    stream_frame_info->temp_byte_size = 0;
#endif
    create_data_demo(stream_frame_info);
}



int execute_command(const char *command) {
    int ret = system(command);
    if (ret == -1) {
        perror("system");
        return -1;
    }
    int exit_status = WEXITSTATUS(ret);
    
    if (exit_status == 0) {
        printf("Command executed successfully.\n");
        return 0;
    } else {
        printf("Command failed with exit status: %d\n", exit_status);
        return exit_status;
    }
}


void log_level_register(log_level_t log_level)
{
    switch(log_level)
    {
        case(DEBUG_PRINT):
        {
            ircmd_log_register(IRCMD_LOG_DEBUG);
            iruvc_log_register(IRUVC_LOG_DEBUG);
            irproc_log_register(IRPROC_LOG_DEBUG);
            irparse_log_register(IRPARSE_LOG_DEBUG);
            irtemp_log_register(IRTEMP_LOG_DEBUG);
            break;
        }
        case(ERROR_PRINT):
        {
            ircmd_log_register(IRCMD_LOG_ERROR);
            iruvc_log_register(IRUVC_LOG_ERROR);
            irproc_log_register(IRPROC_LOG_ERROR);
            irparse_log_register(IRPARSE_LOG_ERROR);
            irtemp_log_register(IRTEMP_LOG_ERROR);
            break;
        }
        case(NO_PRINT):
        default:
        {
            ircmd_log_register(IRCMD_LOG_NO_PRINT);
            iruvc_log_register(IRUVC_LOG_NO_PRINT);
            irproc_log_register(IRPROC_LOG_NO_PRINT);
            irparse_log_register(IRPARSE_LOG_NO_PRINT);
            irtemp_log_register(IRTEMP_LOG_NO_PRINT);
            break;
        }
    }

}


void print_and_record_version(void)
{
    puts(SAMPLE_VERSION);
    puts(irproc_version());
    puts(irparse_version());
    puts(iruvc_version());
    puts(ircmd_version());
    puts(irtemp_version());
#if defined(_WIN32)
    FILE* fp = fopen(".\\libs_version.txt", "wb");
#elif defined(linux) || defined(unix)
    puts(iri2c_version());
    FILE* fp = fopen("./libs_version.txt", "wb");
    fputs(iri2c_version(), fp); fputs("\n", fp);
#endif
    fputs(SAMPLE_VERSION, fp);fputs("\n", fp);
    fputs(irproc_version(), fp);fputs("\n", fp);
    fputs(irparse_version(), fp);fputs("\n", fp);
    fputs(iruvc_version(), fp);fputs("\n", fp);
    fputs(ircmd_version(), fp); fputs("\n", fp);
    fputs(irtemp_version(), fp); fputs("\n", fp);
    fclose(fp);
}


//user's call back function
void usr_test_func(void* frame, void* usr_param)
{
    //write your own callback code here

    char title[80];
    int same_idx = 0;
    StreamFrameInfo_t* stream_frame_info;
    stream_frame_info = (StreamFrameInfo_t*)usr_param;

    if (stream_frame_info == NULL)
    {
        return;
    }


    if ((frame != NULL) && (stream_frame_info->raw_frame != NULL))
    {
        memcpy(stream_frame_info->raw_frame, frame, stream_frame_info->camera_param.frame_size);
    }

    // if (stream_frame_info->raw_frame != NULL)
    // {
    //     raw_data_cut((uint8_t*)stream_frame_info->raw_frame, stream_frame_info->image_byte_size, \
    //         stream_frame_info->temp_byte_size, (uint8_t*)stream_frame_info->image_frame, \
    //         (uint8_t*)stream_frame_info->temp_frame);
    //     same_idx = iruvc_get_same_idx(stream_frame_info->iruvc_handle);
    //     sprintf(title, "Test%d", same_idx);
    //     display_one_frame(stream_frame_info, title);
    // }
    //printf("test_func:%d\n", ((unsigned short*)frame)[10]);

    unsigned int size = 640 *512 *2;
    // static is_save = 0;
    // is_save++;
    // if (is_save == 300)
    // {
    //     FILE *p_fout = fopen("/tmp/test.yuv", "wb");
    //     if (NULL == p_fout) {
    //         perror("Failed to open output file");
    //     } else {
    //         size_t written = fwrite(stream_frame_info->raw_frame, 1,size, p_fout);
    //         if (written != size) {
    //             perror("File write incomplete");
    //         }
    //         fclose(p_fout);
    //     }

    // }
    
    

   
    if (frame_callback != NULL)
    {
        frame_callback(stream_frame_info->raw_frame, size);
    }
    

#if 0
        static int frame_count = 0; // 帧计数器
        const char *output_dir = "/tmp/"; // 输出目录
        char filename[256]; // 文件名缓冲区

        if (access(output_dir, F_OK) != 0) {
            if (mkdir(output_dir, 0777) == -1) {
                perror("Failed to create output directory");
                return;
            }
        }
        frame_count ++;
 

        snprintf(filename, sizeof(filename), 
                "%sive_zoom_out_%04d.yuv", output_dir, frame_count);
        
        FILE *p_fout = fopen(filename, "wb");
        if (NULL == p_fout) {
            perror("Failed to open output file");
        } else {
            size_t written = fwrite(stream_frame_info->image_frame, 1,size, p_fout);
            if (written != size) {
                perror("File write incomplete");
            }
            fclose(p_fout);
            printf("Saved to %s\n", filename);
            frame_count++; 
        }
#endif



    //printf("@@ frame_idx:%d\n", frame_idx);
    frame_idx++;
}

#if 0
void* thread_function(void* threadarg)
{
    int rst;
    StreamFrameInfo_t *stream_frame_info = (StreamFrameInfo_t*)threadarg;
    stream_frame_info->is_stop = 0;
    //user function callback mode
#ifdef USER_FUNCTION_CALLBACK
    display_init(stream_frame_info);
    rst = ir_camera_stream_on_with_callback(stream_frame_info, (void *)usr_test_func);

    if (rst < 0)
    {
        puts("ir camera stream on failed!\n");
        return 0;
    }

    while(stream_frame_info->is_stop == 0){
        usleep(100000);
    }

    ir_camera_stream_off_with_callback(stream_frame_info);
    display_release(stream_frame_info);
#else
    //multiple thread function mode
    int whther_temp_output = 0;
#if defined(TEMP_OUTPUT)
    whther_temp_output = 1;
#endif
    rst = ir_camera_stream_on(stream_frame_info, whther_temp_output);
    if (rst < 0)
    {
        puts("ir camera stream on failed!\n");
        getchar();
        return 0;
    }

    pthread_t tid_stream, tid_display, tid_temperature;

    //pthread_create(&tid_display, NULL, display_function, stream_frame_info);
    pthread_create(&tid_stream, NULL, stream_function, stream_frame_info);

    //pthread_join(tid_display, NULL);
    pthread_join(tid_stream, NULL);
#endif
    return 0;
}
#endif



static StreamFrameInfo_t g_stream_frame_info = { 0 };
static IruvcHandle_t* g_iruvc_handle = NULL;

int register_stream_callback(StreamFrameInfo_t *stream_frame_info)
{

    if (stream_frame_info == NULL)
    {
        printf("stream_frame_info is NULL\n");
        return -1;
    }
    
    int rst;
    stream_frame_info->is_stop = 0;

    display_init(stream_frame_info);
    rst = ir_camera_stream_on_with_callback(stream_frame_info, (void *)usr_test_func);

    if (rst < 0)
    {
        puts("ir camera stream on failed!\n");
        return 0;
    }

    return rst;
}

int stop_iray_proc(void)
{
    ir_camera_stream_off_with_callback(&g_stream_frame_info);
    display_release(&g_stream_frame_info);
    iruvc_camera_close(g_iruvc_handle);
}


#if 0
int start_iray_proc(void *callback)
{
    //set priority to highest level
#if defined(_WIN32)
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
#elif defined(linux) || defined(unix)
    setpriority(PRIO_PROCESS, 0, -20);
#endif

    frame_callback = callback;

    //version
    print_and_record_version();
    log_level_register(ERROR_PRINT);

  
    int rst, same_idx, resolution_idx;

#if defined(IMAGE_AND_TEMP_OUTPUT)
    resolution_idx = 1;
#elif defined(IMAGE_OUTPUT) || defined(TEMP_OUTPUT)
    resolution_idx = 0;
#endif

    //open camera 0
    same_idx = 0;
    StreamFrameInfo_t stream_frame_info1 = { 0 };
    IruvcHandle_t* iruvc_handle1 = iruvc_create_handle();
    printf("thread_function same index:%d\n", same_idx);
    rst = ir_camera_open(iruvc_handle1, &stream_frame_info1.camera_param, same_idx, resolution_idx);
    if (rst < 0)
    {
        puts("ir camera open failed!\n");
        getchar();
        return 0;
    }
    load_stream_frame_info(&stream_frame_info1);
    stream_frame_info1.iruvc_handle = iruvc_handle1;
    stream_frame_info1.ircmd_handle = ircmd_create_handle(iruvc_handle1, VDCMD_I2C_USB_VDCMD);
    
    pthread_t tid1, tid2, tid_cmd1, tid_cmd2;
    pthread_create(&tid1, NULL, thread_function, (void*)&stream_frame_info1);

    pthread_create(&tid_cmd1, NULL, cmd_function, (void*)&stream_frame_info1);
    pthread_join(tid1, NULL);
    pthread_cancel(tid_cmd1);
    iruvc_camera_close(iruvc_handle1);

    return 0;
}
#endif


int start_iray_proc(void *callback)
{
    system("echo start_iray_proc >> /tmp/ir.log");
    //set priority to highest level
#if defined(_WIN32)
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
#elif defined(linux) || defined(unix)
    setpriority(PRIO_PROCESS, 0, -20);
#endif

    frame_callback = callback;

    system("echo print_and_record_version1 >> /tmp/ir.log");
    //version
    //print_and_record_version();
    //log_level_register(ERROR_PRINT);

    system("echo print_and_record_version2 >> /tmp/ir.log");
    int rst, same_idx, resolution_idx;

#if defined(IMAGE_AND_TEMP_OUTPUT)
    resolution_idx = 1;
#elif defined(IMAGE_OUTPUT) || defined(TEMP_OUTPUT)
    resolution_idx = 0;
#endif
    system("echo print_and_record_version3 >> /tmp/ir.log");
    //open camera 0
    same_idx = 0;
    g_iruvc_handle = iruvc_create_handle();
    if (g_iruvc_handle == NULL)
    {
        printf("ir camera create handle failed!\n");
        return 0;
    }

    printf("thread_function same index:%d\n", same_idx);
    rst = ir_camera_open(g_iruvc_handle, &g_stream_frame_info.camera_param, same_idx, resolution_idx);
    if (rst < 0)
    {
        puts("ir camera open failed!\n");
        getchar();
        return 0;
    }
    load_stream_frame_info(&g_stream_frame_info);
    g_stream_frame_info.iruvc_handle = g_iruvc_handle;
    g_stream_frame_info.ircmd_handle = ircmd_create_handle(g_iruvc_handle, VDCMD_I2C_USB_VDCMD);

    register_stream_callback(&g_stream_frame_info);
    return 0;
}




int measure_point_temperature(IrcmdPoint_t *point_pos, uint16_t *point_temp_value)
{
    return cmd_measure_point_temperature(&g_stream_frame_info, point_pos, point_temp_value);
}

int measure_line_temperature(IrcmdLine_t *line , TpdLineRectTempInfo_t *line_temp_info)
{
    return cmd_measure_line_temperature(&g_stream_frame_info, line, line_temp_info);
}


int measure_rect_temperature(IrcmdRect_t *rect1, TpdLineRectTempInfo_t *rect1_temp_info)
{
    return cmd_measure_rect_temperature(&g_stream_frame_info, rect1, rect1_temp_info);
}

int measure_frame_temperature(FrameTempInfo_t *frame_temp_info)
{
    return cmd_measure_frame_temperature(&g_stream_frame_info, frame_temp_info);
}

int ctrl_shutter_onoff(uint16_t *value)
{
    return cmd_ctrl_shutter_onoff(&g_stream_frame_info, value);
}


int ctrl_pseduo_color(uint16_t *value)
{
    return cmd_ctrl_pseduo_color(&g_stream_frame_info, value);
}


#ifdef __cplusplus
}
#endif
