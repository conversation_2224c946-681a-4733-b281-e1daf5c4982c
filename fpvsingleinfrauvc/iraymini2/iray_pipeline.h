#ifndef IRAY_PIPELINE_H
#define IRAY_PIPELINE_H

#include "vs_type.h"
#include "vs_mal_vbm.h"
#include "sample_common.h"
#include "uvc.h"

#ifdef __cplusplus
extern "C" {
#endif



vs_int32_t iray_uvc_pipeline_create(vs_void_t* p_camera_info);
vs_int32_t iray_uvc_pipeline_destroy(vs_void_t* p_camera_info);
vs_int32_t iray_uvc_player_framesend_to_vpp(vs_uvc_camera_info_s* p_camera);

#ifdef __cplusplus
}
#endif


#endif