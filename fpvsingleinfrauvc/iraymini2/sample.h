#ifndef SAMPLE_H_
#define SAMPLE_H_

#if defined(_WIN32)
#include <Windows.h>
#elif defined(linux) || defined(unix)
#include <unistd.h>
#include <sys/time.h>
#include <sys/resource.h>
#endif

#include <stdio.h>

typedef enum {
	DEBUG_PRINT = 0,
	ERROR_PRINT,
	NO_PRINT,
}log_level_t;

typedef void (*frame_callback_func)(void*, unsigned int);

#define USER_FUNCTION_CALLBACK

//#define IMAGE_AND_TEMP_OUTPUT	//normal mode:get 1 image frame and temp frame at the same time eg:640*1024
#define IMAGE_OUTPUT	//only image frame eg:640*512
//#define TEMP_OUTPUT		//only temp frame eg:640*512

int start_iray_proc(void *callback);
// int ctrl_shutter_onoff(uint16_t *value);
// int ctrl_pseduo_color(uint16_t *value);
// int execute_command(const char *command);
#endif

