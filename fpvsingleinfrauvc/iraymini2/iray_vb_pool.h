#ifndef IRAY_VB_POOL_H_
#define IRAY_VB_POOL_H_


#include <linux/videodev2.h>
#include "vs_type.h"
#include "vs_comm_video.h"
#include "vs_mal_vbm.h"
#include "uvc.h"
#ifdef __cplusplus
extern "C" {
#endif

vs_int32_t uvc_framebuf_vb_acquire(vs_void_t* p_camera_info);
vs_int32_t uvc_framebuf_vb_release(vs_void_t *p_camera_info);
vs_int32_t uvc_framebuf_vb_acquire_with_poolid(vs_video_frame_info_s *frame_info, vs_uint32_t poolid, vs_pixel_format_e format);
vs_int32_t uvc_framebuf_vb_release_ex(vs_video_frame_s *p_frame, vs_uint32_t blk_size);
int iray_vb_pool_init(vs_uvc_camera_info_s *p_camera);
int iray_vb_pool_deinit(vs_void_t* p_camera_info);

#ifdef __cplusplus
}
#endif


#endif