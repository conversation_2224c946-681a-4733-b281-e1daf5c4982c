#!/bin/bash

# 测试Makefile的IRAY版本选择功能
# 这个脚本用于验证Makefile是否正确处理mini1和mini2的选择

echo "=== 测试Makefile的IRAY版本选择功能 ==="
echo

# 测试默认情况 (mini1)
echo "1. 测试默认情况 (应该使用mini1):"
echo "   执行: make -n | grep -E '(IRAY_INCLUDE_DIR|IRAY_LIB_DIR|IRAY_LIBS)'"
make -n 2>/dev/null | grep -E "(-I\./include|-L\./lib|liri2c|lircmd|liruvc|lirprocess|lirparse|lirtemp)" | head -5
echo

# 测试USE_IRAY_MINI2=1的情况
echo "2. 测试USE_IRAY_MINI2=1 (应该使用mini2):"
echo "   执行: make USE_IRAY_MINI2=1 -n | grep -E '(mini2|IRAY)'"
make USE_IRAY_MINI2=1 -n 2>/dev/null | grep -E "(-I\.\./mini2/include|-L\.\./mini2/lib|DUSE_IRAY_MINI2)" | head -5
echo

# 显示变量值
echo "3. 显示Makefile变量值:"
echo "   默认情况下的变量:"
make -f - <<'EOF' 2>/dev/null
include Makefile
show-vars:
	@echo "IRAY_INCLUDE_DIR = $(IRAY_INCLUDE_DIR)"
	@echo "IRAY_LIB_DIR = $(IRAY_LIB_DIR)"
	@echo "IRAY_LIBS = $(IRAY_LIBS)"
EOF
make show-vars 2>/dev/null

echo
echo "   USE_IRAY_MINI2=1 情况下的变量:"
make USE_IRAY_MINI2=1 -f - <<'EOF' 2>/dev/null
include Makefile
show-vars:
	@echo "IRAY_INCLUDE_DIR = $(IRAY_INCLUDE_DIR)"
	@echo "IRAY_LIB_DIR = $(IRAY_LIB_DIR)"
	@echo "IRAY_LIBS = $(IRAY_LIBS)"
EOF
make USE_IRAY_MINI2=1 show-vars 2>/dev/null

echo
echo "=== 测试完成 ==="
echo
echo "使用说明:"
echo "- 默认编译 (mini1): make"
echo "- 使用mini2编译: make USE_IRAY_MINI2=1"
echo "- 或设置环境变量: export USE_IRAY_MINI2=1 && make"
