#
# @file    Makefile
# <AUTHOR> Software Group
# @date    2021-11-09
# @version v1.0
# @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
#

PROJECT_HOME = ./
CXX =aarch64-linux-gnu-g++
CC =aarch64-linux-gnu-gcc

MP_CFLAGS += -DVS_CARINA

SENSOR0_TYPE ?= SONY_IMX334_MIPI_8M_60FPS_12BIT
SENSOR1_TYPE ?= OV_OS04A10_MIPI_4M_30FPS_10BIT
SENSOR2_TYPE ?= OV_OS04A10_MIPI_4M_30FPS_10BIT
SENSOR3_TYPE ?= OV_OS04A10_MIPI_4M_30FPS_10BIT

MP_CFLAGS += -DSENSOR0_TYPE=$(SENSOR0_TYPE)
MP_CFLAGS += -DSENSOR1_TYPE=$(SENSOR1_TYPE)
MP_CFLAGS += -DSENSOR2_TYPE=$(SENSOR2_TYPE)
MP_CFLAGS += -DSENSOR3_TYPE=$(SENSOR3_TYPE)


OUTBIN_DIR = ../bin/
COMMON_DIR = ../vs_common/

LINK_TARGET = rawinfrauvc

COMMON_SRCS += $(COMMON_DIR)/sample_common_sys.c
COMMON_SRCS += $(COMMON_DIR)/sample_common_vii.c
COMMON_SRCS += $(COMMON_DIR)/sample_common_vpp.c
COMMON_SRCS += $(COMMON_DIR)/sample_common_vdec.c
#COMMON_SRCS += $(COMMON_DIR)/sample_common_vo.c
COMMON_SRCS += $(COMMON_DIR)/sample_common_mipitx.c
COMMON_SRCS += $(COMMON_DIR)/sample_common_venc.c
OTHER_SRCS += ../configparser/src/configparser.c
OTHER_SRCS += $(wildcard ../ipc/*.c)
# OTHER_SRCS += $(wildcard ./ipc/store/*.c)
#OTHER_SRCS += $(wildcard ../ipc/video/*.c)
OTHER_SRCS += $(wildcard ../ipc/infra/*.c)

SOURCES = $(wildcard *.c)
SOURCES += $(COMMON_SRCS)
SOURCES += $(OTHER_SRCS)
OBJS = $(SOURCES:.c=.o)


CFLAGS += -I../vs839_libs/include
CFLAGS += -I../vs_common
CFLAGS += -I../configparser/include

# CFLAGS += -I./ipc -I./ipc/algo
# CFLAGS += -I./ipc -I./ipc/store
#CFLAGS += -I../ipc -I../ipc/video
CFLAGS += -I../ipc -I../ipc/infra

CFLAGS += -I./include
CFLAGS += -I./


#LD_LIB_PATH = /opt/nfs3/M1_lib/
LD_LIB_PATH = ../vs839_libs/lib/
NN_LD_LIB_PATH = ../vs839_libs/lib/nn/

LDFLAGS += -L $(LD_LIB_PATH)  -lvii \
           -L $(LD_LIB_PATH)  -lvpp -ldl \
           -L $(LD_LIB_PATH)  -lbase -lsys -linit -ldsp -lgpe \
           -L $(LD_LIB_PATH)  -lisp \
           -L $(LD_LIB_PATH)  -lmipirx \
           -L $(LD_LIB_PATH)  -lvpp \
           -L $(LD_LIB_PATH)  -lvenc \
           -L $(LD_LIB_PATH)  -lvdec \
           -L $(LD_LIB_PATH)  -lgdc \
           -L $(LD_LIB_PATH)  -lvo \
           -L $(LD_LIB_PATH)  -live \
           -L $(LD_LIB_PATH)  -lmipitx \
           -L $(NN_LD_LIB_PATH)  -lGAL 
# LDFLAGS += -L $(LD_LIB_PATH)  -lhdmi
#LDFLAGS += -L/opt/nfs3/lib/  -lnanomsg
LDFLAGS += -lpthread -lm -lstdc++ -ldl -Wall -Ofast -Wno-unused-function -fstack-protector-strong -fPIC

LDFLAGS += -L./lib
LDFLAGS += -Wl,-rpath=./lib
#LDFLAGS += -liri2c -lircmd -liruvc -lirprocess -lirparse -lirtemp
LDFLAGS += -lircam -lircmd -liruvc -lusb-1.0 -lm  -lnanomsg
## customer_id: HEQ, MMC...
CUS_ID ?= HEQ
#CFLAGS += -DGOP_MODE_NORMP_SKIP_LEVEL=3
CFLAGS += -DGOP_MODE_NORMP
#CUS_ID ?= MMC

CFLAGS += -DGOP_MODE_NORMP_SKIP_LEVEL=5

CFLAGS += -DAIRVISEN_VENC_BITRATE_FORCE_3M

CFLAGS += -DUSE_IRAY_SDK


REBUILDABLES = $(OBJS) $(LINK_TARGET)
#CFLAGS += -DFHD_OUTPUT_ENABLED
CFLAGS += -DVIDEO_ZOOM_ENABLED #enable ive dma copy and ive resize, use ipc control
CFLAGS += -DUVC_WIDTH=640
CFLAGS += -DUVC_HEIGHT=512
CFLAGS += -DCVBS_WIDTH=720
CFLAGS += -DCVBS_HEIGHT=576
CFLAGS += -DUVC_FPS=50
CFLAGS += -DVENC_ENABLED
#CFLAGS += -DVENC_OUTPUT_FHD
#CFLAGS += -DVO_ENABLED
#CFLAGS += -DWORK_ALONE_WITHOUT_NN
CFLAGS += -DWORK_WITH_RGB
CFLAGS += -DWORK_WITH_2_MIPI
CFLAGS += -DWORKING_ON_RAW_MODE
#CFLAGS += -DFRAME_CTRL_ENABLED
CFLAGS += -DIR_UVC_VPP_GRP_ID=3
CFLAGS += -DIR_UVC_VPP_CHN_ID=3
CFLAGS += -DIR_UVC_VENC_CHN_ID=3
CFLAGS += -DVPP_EXT_FHD_OUTPUT_ENABLED
CFLAGS += -DNN_NANOTRACK_ENABLED 
CFLAGS += -DNN_NANOTRACK_WIDTH=640
CFLAGS += -DNN_NANOTRACK_HEIGHT=512
CFLAGS += -DNN_DETECT_ENABLED 
CFLAGS += -DNN_DETECT_WIDTH=640
CFLAGS += -DNN_DETECT_HEIGHT=512
CFLAGS += -DIR_UVC_VPP_NN_NANOTRACK_CHN_ID=2
CFLAGS += -DIR_UVC_VPP_NN_DETECT_CHN_ID=1

.PHONY: all
all : $(LINK_TARGET)
	@echo $(LINK_TARGET) compile done!
	@cp $(OUTBIN_DIR)/$(LINK_TARGET)  /opt/nfs3/

$(LINK_TARGET) : $(OBJS)
	@/bin/bash ../sensitivepurger/purgesensitiveinfo.sh $^
	@$(CC) $(OBJS) $(LDFLAGS) -o $@
	@mkdir -p $(OUTBIN_DIR) && mv -f $@ $(OUTBIN_DIR)

$(OBJS):%.o:%.c
	@$(CC) $(CFLAGS) $(MP_CFLAGS) -c -o $@ $<

clean :
	@rm -f $(REBUILDABLES)
	@echo $(LINK_TARGET) Clean done!
