/**
 * @file    sample_uvc_camera.c
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <linux/videodev2.h>

#include "iray_vb_pool.h"
#include "uvc.h"
#include "uvc_util.h"
#include "sample_common.h"
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"
// #include "video_zoom_message.h"
#include "sample.h"
#include "iray_pipeline.h"
#include "ir_ctrl_message.h"

ir_request_measure_temperature_s g_measure_param = {0};
vs_uvc_camera_info_s camera_info = {0};

typedef enum airvisen_infra_uvc_host_status_type
{
    E_INFRA_UVC_HOST_STATUS_STOP = 1,
    E_INFRA_UVC_HOST_STATUS_DEINIT = 2,
    E_INFRA_UVC_HOST_STATUS_CLOSE = 4,
} airvisen_infra_uvc_host_status_type_e;

vs_uint32_t g_pixel_formats[] = {
    V4L2_PIX_FMT_YUYV,
    V4L2_PIX_FMT_NV12,
    V4L2_PIX_FMT_MJPEG,
    V4L2_PIX_FMT_H264,
    V4L2_PIX_FMT_H265,
    V4L2_PIX_FMT_HEVC};

vs_void_t uvc_usage(char *argv[])
{
    vs_int32_t i = 0;

    printf("use age: %s [options] \n", argv[0]);
    printf("all options are as follows:\n");
    printf("-d:\t set uvc device path.\n");
    printf("-s:\t set out video size (eg. 3840*2160) \n");
    printf("-f:\t set frame rate. \n");
    printf("-p:\t set video pixelformat. \n");
    printf("\t\t pixelformat optional values: \n");
    for (i = 0; i < sizeof(g_pixel_formats) / sizeof(vs_uint32_t); i++)
    {
        printf("\t\t %d (means:%s)\n", i, (char *)vs_uvc_v4l2_pixelformat_str_get(g_pixel_formats[i]));
    }
    printf("example: ./sample_uvc_host -d /dev/video0 -s 3840*2160 -f 60 -p 0\n");
}

vs_int32_t parse_param(vs_int32_t argc, char *argv[], vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t opt;
    char *p_end_str;

    if (argc < 5)
    {
        uvc_usage(argv);
        return VS_FAILED;
    }

    while ((opt = getopt(argc, argv, (char *)"d:s:f:p:h")) != -1)
    {
        switch (opt)
        {
        case 'h':
            uvc_usage(argv);
            return VS_FAILED;
        case 'd':
            sprintf((char *)p_camera->dev_path, "%s", optarg);
            if (access((char *)p_camera->dev_path, F_OK) != 0)
            {
                printf("illegal param [%s], can not find the usb camera device \n", optarg);
                return VS_FAILED;
            }
            // printf("width_s[%s] \n", width_s);
            break;
        case 's':
            p_camera->width = strtol(optarg, &p_end_str, 10);
            if (p_end_str[0] != '*' || p_camera->width == 0)
            {
                printf("illegal param [%s] \n", optarg);
                return VS_FAILED;
            }
            p_camera->height = strtol(p_end_str + 1, &p_end_str, 10);
            if (p_end_str[0] != '\0' || p_camera->height == 0)
            {
                printf("illegal param [%s] \n", optarg);
                return VS_FAILED;
            }
            // printf("height_s[%s] \n", height_s);
            break;
        case 'p':
            if (atoi(optarg) < 0 || atoi(optarg) >= sizeof(g_pixel_formats) / sizeof(vs_uint32_t))
            {
                printf("illegal param [%s] \n", optarg);
                return VS_FAILED;
            }
            p_camera->pixelformat = g_pixel_formats[atoi(optarg)];
            if (p_camera->pixelformat == 0)
            {
                printf("illegal param [%s] \n", optarg);
                return VS_FAILED;
            }
            // printf("fps_s[%s] \n", fps_s);
            break;
        case 'f':
            p_camera->fps = atoi(optarg);
            if (atoi(optarg) < 0 || atoi(optarg) > 300)
            {
                printf("illegal param [%s] \n", optarg);
                return VS_FAILED;
            }
            // printf("fps_s[%s] \n", fps_s);
            break;
        default:
            printf("unkown option: %c, run %s -h for help.\n", opt, argv[0]);
            uvc_usage(argv);
            return VS_FAILED;
        }
        // printf("argv[%d]=%s\n", optind, argv[optind]);
    }

    printf("dev_path[%s] width[%u] height[%u] fps[%u] pixelformat[%u]\n", (char *)p_camera->dev_path,
           p_camera->width, p_camera->height, p_camera->fps, p_camera->pixelformat);
    return VS_SUCCESS;
}

static vs_void_t *ipc_message_proc(vs_void_t *arg)
{
    vs_uvc_camera_info_s *p_uvc_camera_info_param = (vs_uvc_camera_info_s *)arg;
    // add ipc, recv capture command
    pthread_setname_np(pthread_self(), "ipcmsgproc");
    while (!p_uvc_camera_info_param->is_stop_uvc_camera)
    {
        printf("Recv VIDEO ZOOM command\n");
        char ipc_data[256] = {0};
        int rc = airvisen_recv_ir_ctrl_message(ipc_data, 256);
        if (rc > 0)
        {
            if (rc < sizeof(airvisen_ipc_message_head_s))
            {
                printf("recv error message\n");
                continue;
            }

            airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s *)ipc_data;
            switch (msg->cmd)
            {
            case IR_VIDEO_ZOOM:
            {
                ir_video_zoom_message_s *video_zoom_param = (ir_video_zoom_message_s *)ipc_data;
                printf("Recv VIDEO ZOOM command, mode: %d  factor:%d\n", video_zoom_param->mode, video_zoom_param->zoom_spec_factor);

                if (video_zoom_param->mode == 0)
                {
                    p_uvc_camera_info_param->zoom_factor = p_uvc_camera_info_param->zoom_factor + 1 > 80 ? 80 : p_uvc_camera_info_param->zoom_factor + 1;
                }
                else if (video_zoom_param->mode == 1)
                {
                    p_uvc_camera_info_param->zoom_factor = p_uvc_camera_info_param->zoom_factor - 1 < 10 ? 10 : p_uvc_camera_info_param->zoom_factor - 1;
                }
                else
                {
                    p_uvc_camera_info_param->zoom_factor = video_zoom_param->zoom_spec_factor;
                }

                break;
            }
            // case IR_SHUTTER:
            // {
            //     printf("[%s] IR_SHUTTER\n", __func__);
            //     ir_shutter_message_s *msg = (ir_shutter_message_s *)ipc_data;
            //     ctrl_shutter_onoff(&msg->state);
            //     break;
            // }
            // case IR_CTRL_PSEDUO_COLOR:
            // {
            //     printf("IR_CTRL_PSEDUO_COLOR\n");
            //     ir_pseudo_color_message_s *msg = (ir_pseudo_color_message_s *)ipc_data;
            //     printf("msg->mode = %d\n", msg->mode);
            //     ctrl_pseduo_color(&msg->mode);
            //     break;
            // }
            default:
                printf("unsupport command: [%d]\n", msg->cmd);
                break;
            }
        }
        else
        {
            vs_sample_trace("ipc_message_proc recv message failed\n");
        }
    }

    return NULL;
}

/**
 * ipc thread
 */
vs_int32_t ipc_message_proc_start(vs_uvc_camera_info_s *p_uvc_camera_info_param)
{
    vs_sample_trace("ipc_message_proc_start\n");
    pthread_t tid;
    return pthread_create(&tid, 0, ipc_message_proc, p_uvc_camera_info_param);
}

// #include <time.h>

// // 定义全局变量来记录时间和计数
// static time_t last_time = 0;
// static unsigned int count = 0;
void *on_frame_callback(void *frame, unsigned int size)
{

    // printf("on_frame_callback\n");
    camera_info.buf->start = frame;
    camera_info.buf->length = size;

    // 记录当前时间
    // time_t current_time = time(NULL);

    // // 如果时间间隔超过 1 秒，输出统计结果
    // if (current_time != last_time) {
    //     if (last_time != 0) {
    //         printf("Callback called %u times in the last second.\n", count);
    //     }
    //     last_time = current_time;
    //     count = 0; // 重置计数器
    // }

    // // 增加计数
    // count++;

    //
    iray_uvc_player_framesend_to_vpp(&camera_info);
}

int g_traceinfo = 1;
vs_int32_t main(vs_int32_t argc, char *argv[])
{
    pthread_setname_np(pthread_self(), "main");
    printf("buildInfo: %s, %s\n", __DATE__, __TIME__);
    vs_int32_t ret = VS_SUCCESS;

    vs_int32_t width = UVC_WIDTH, height = UVC_HEIGHT, fps = UVC_FPS;

    memset(&camera_info, 0, sizeof(vs_uvc_camera_info_s));
    // ret = parse_param(argc, argv, &camera_info);
    // if (ret != VS_SUCCESS) {
    //     VS_UVC_LOG_E("UVC: parse_param failed: ret(0x%x).\n", ret);
    //     return ret;
    // }

    sprintf((char *)camera_info.dev_path, "%s", "/dev/video0");

    camera_info.width = width;
    camera_info.height = height;
    camera_info.fps = fps;
    camera_info.pixelformat = V4L2_PIX_FMT_YUYV;
    camera_info.buf_count = 4;
    camera_info.mem_type = V4L2_MEMORY_MMAP;

    // #ifdef VIDEO_ZOOM_ENABLED
    // init video zoom ipc
    airvisen_start_ir_ctrl_client_ipc();
    ret = ipc_message_proc_start(&camera_info);
    if (ret != VS_SUCCESS)
    {
        vs_sample_trace("ipc_message_proc_start failed, ret[%d]\n", ret);
    }
    // default zoom factor;
    camera_info.zoom_factor = 1;
    // #endif

    // vpp
    iray_uvc_pipeline_create(&camera_info);

    // init vb
    iray_vb_pool_init(&camera_info);

    // iray proc
    start_iray_proc(on_frame_callback);

    // pause
    sample_common_pause();

    // TODO
    iray_vb_pool_deinit(&camera_info);
    camera_info.is_stop_uvc_camera = 1;
    pthread_join(camera_info.uvc_camera_td, VS_NULL);

    // main_uvc_camera_stop:
    return VS_SUCCESS;
}
